# FastAPI + PostgreSQL Docker 单容器部署指南

本项目提供了一个完整的 Docker 化解决方案，将 FastAPI 后端和 PostgreSQL 数据库打包在同一个容器中，**包含完整的数据库结构和数据**，实现真正的免调试部署。

## ✨ 特性

- 🚀 **一键部署**：包含完整的数据库结构和初始数据
- 🔧 **免调试**：使用现有数据库备份，无需手动初始化
- 🌐 **网络优化**：配置国内镜像源，解决网络连接问题
- 📊 **完整数据**：包含所有表结构、索引、约束和初始数据
- 🛡️ **健康检查**：自动监控服务状态
- 📝 **详细日志**：完整的启动和运行日志

## 🚀 快速开始

### 前置要求

1. **安装 Docker**
   ```bash
   # macOS (使用 Homebrew)
   brew install docker
   
   # 或下载 Docker Desktop
   # https://www.docker.com/products/docker-desktop
   
   # Ubuntu/Debian
   sudo apt-get update
   sudo apt-get install docker.io docker-compose
   
   # CentOS/RHEL
   sudo yum install docker docker-compose
   ```

2. **启动 Docker 服务**
   ```bash
   # Linux
   sudo systemctl start docker
   sudo systemctl enable docker
   
   # macOS - 启动 Docker Desktop 应用
   ```

3. **验证 Docker 安装**
   ```bash
   docker --version
   docker-compose --version
   ```

### 一键部署

1. **网络诊断（可选）**
   ```bash
   # 检查网络连接状态
   ./docker-build.sh diagnose
   ```

2. **构建并运行容器**
   ```bash
   # 给脚本执行权限
   chmod +x docker-build.sh

   # 构建镜像（包含完整数据库）
   ./docker-build.sh build

   # 启动容器
   ./docker-build.sh run
   ```

2. **访问应用**
   - API 服务: http://localhost:8000
   - API 文档: http://localhost:8000/docs
   - ReDoc 文档: http://localhost:8000/redoc
   - 健康检查: http://localhost:8000/health

## 📁 Docker 文件说明

### 核心文件

- `Dockerfile` - 多阶段构建配置，包含 Python 和 PostgreSQL
- `docker-compose.yml` - 容器编排配置
- `docker-entrypoint.sh` - 容器启动脚本
- `docker-build.sh` - 管理脚本（构建、运行、停止等）
- `.dockerignore` - Docker 构建忽略文件
- `.env.docker` - Docker 环境变量配置

### 容器架构

```
┌─────────────────────────────────────┐
│           Docker 容器               │
├─────────────────────────────────────┤
│  ┌─────────────┐  ┌───────────────┐ │
│  │   FastAPI   │  │  PostgreSQL   │ │
│  │   (8000)    │◄─┤   (5432)      │ │
│  │             │  │               │ │
│  └─────────────┘  └───────────────┘ │
├─────────────────────────────────────┤
│         Ubuntu 22.04 基础镜像        │
└─────────────────────────────────────┘
```

## 🛠️ 管理命令

使用 `docker-build.sh` 脚本管理容器：

```bash
# 构建镜像
./docker-build.sh build

# 启动容器
./docker-build.sh run

# 停止容器
./docker-build.sh stop

# 重启容器
./docker-build.sh restart

# 查看日志
./docker-build.sh logs
./docker-build.sh logs -f  # 实时日志

# 查看状态
./docker-build.sh status

# 进入容器
./docker-build.sh shell

# 清理资源
./docker-build.sh clean

# 显示帮助
./docker-build.sh help
```

## 🔧 配置说明

### 环境变量

主要配置在 `docker-compose.yml` 和 `.env.docker` 中：

```yaml
# 数据库配置（容器内）
DATABASE_URL: postgresql://fastapi_user:7EbCi8ekZD5hbbHr@localhost:5432/qichejie

# 应用配置
APP_NAME: FastAPI Server
DEBUG: false
ENVIRONMENT: production

# 安全配置（生产环境请修改）
SECRET_KEY: your-super-secret-key-change-this-in-production-docker
```

### 端口映射

- 容器内端口 8000 映射到主机端口 8000
- PostgreSQL 在容器内运行，不对外暴露

### 数据持久化

使用 Docker 卷持久化数据：

```yaml
volumes:
  - postgres_data:/var/lib/postgresql/14/main  # 数据库数据
  - app_logs:/app/logs                         # 应用日志
  - app_uploads:/app/uploads                   # 上传文件
```

## 🔍 故障排除

### 常见问题

1. **容器启动失败**
   ```bash
   # 查看详细日志
   ./docker-build.sh logs
   
   # 检查容器状态
   ./docker-build.sh status
   ```

2. **数据库连接失败**
   ```bash
   # 进入容器检查
   ./docker-build.sh shell
   
   # 在容器内检查 PostgreSQL
   su - postgres
   psql -l
   ```

3. **端口被占用**
   ```bash
   # 检查端口占用
   lsof -i :8000
   
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "8001:8000"  # 改为其他端口
   ```

4. **权限问题**
   ```bash
   # 确保脚本有执行权限
   chmod +x docker-build.sh docker-entrypoint.sh
   ```

### 日志查看

```bash
# 应用日志
./docker-build.sh logs

# 进入容器查看详细日志
./docker-build.sh shell
tail -f /var/log/supervisor/fastapi.log
tail -f /var/log/supervisor/postgresql.log
```

## 🚀 生产环境部署

### 安全配置

1. **修改默认密码**
   ```bash
   # 编辑 docker-compose.yml
   - SECRET_KEY=your-production-secret-key
   - DATABASE_PASSWORD=your-secure-password
   ```

2. **限制 CORS**
   ```bash
   # 编辑 docker-compose.yml
   - CORS_ORIGINS=["https://yourdomain.com"]
   ```

3. **禁用调试模式**
   ```bash
   - DEBUG=false
   - DOCS_URL=null  # 禁用 API 文档
   ```

### 性能优化

1. **资源限制**
   ```yaml
   # 在 docker-compose.yml 中添加
   deploy:
     resources:
       limits:
         memory: 1G
         cpus: '0.5'
   ```

2. **健康检查**
   ```yaml
   healthcheck:
     test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
     interval: 30s
     timeout: 10s
     retries: 3
   ```

## 📝 开发说明

### 本地开发

如果需要在开发环境中使用：

```bash
# 使用开发配置
cp .env.example .env
# 编辑 .env 文件

# 或直接运行 Python 应用
python3 run.py
```

### 代码修改

容器构建时会复制所有代码，如需修改：

1. 修改代码
2. 重新构建镜像：`./docker-build.sh build`
3. 重启容器：`./docker-build.sh restart`

## 📞 支持

如遇问题，请检查：

1. Docker 和 docker-compose 是否正确安装
2. 端口 8000 是否被占用
3. 系统资源是否充足（至少 1GB 内存）
4. 防火墙设置是否允许端口访问

---

**注意**: 这是一个单容器解决方案，适合开发、测试和小规模部署。生产环境建议使用分离的数据库服务。
