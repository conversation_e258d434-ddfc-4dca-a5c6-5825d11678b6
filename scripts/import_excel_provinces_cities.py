#!/usr/bin/env python3
"""
从五级行政区划Excel文件导入省市数据到数据库
一次性脚本，用于替换现有的硬编码省市数据
"""

import sys
import uuid
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.orm import sessionmaker
from app.database import engine
from app.models.user import Province, City
from app.utils.timezone import to_utc_for_db
import structlog

logger = structlog.get_logger("excel_import")


def read_excel_data(excel_path: str) -> pd.DataFrame:
    """读取Excel文件数据"""
    try:
        logger.info("开始读取Excel文件", path=excel_path)
        df = pd.read_excel(excel_path)
        logger.info("Excel文件读取成功", 
                   rows=len(df), 
                   columns=list(df.columns))
        return df
    except Exception as e:
        logger.error("读取Excel文件失败", error=str(e))
        raise


def extract_provinces_data(df: pd.DataFrame) -> List[Dict]:
    """提取省份数据（层级1）"""
    logger.info("开始提取省份数据")
    
    # 筛选层级1的数据
    provinces_df = df[df['层级'] == 1].copy()
    
    provinces_data = []
    for _, row in provinces_df.iterrows():
        province_data = {
            'code': str(row['字典项值']).zfill(2),  # 确保是2位数字
            'name': row['字典项名称']
        }
        provinces_data.append(province_data)
    
    logger.info("省份数据提取完成", count=len(provinces_data))
    return provinces_data


def extract_cities_data(df: pd.DataFrame) -> List[Dict]:
    """提取城市数据（层级2，智能过滤）"""
    logger.info("开始提取城市数据")
    
    # 筛选层级2的数据
    cities_df = df[df['层级'] == 2].copy()
    
    # 智能过滤：排除中间层级和特殊情况
    exclude_keywords = ['市辖区', '县', '省直辖', '自治区直辖']
    filtered_cities = cities_df[
        ~cities_df['字典项名称'].str.contains('|'.join(exclude_keywords), na=False)
    ]
    
    cities_data = []
    for _, row in filtered_cities.iterrows():
        city_data = {
            'code': str(row['字典项值']),
            'name': row['字典项名称'],
            'province_code': str(row['父字典项值']).zfill(2)  # 确保是2位数字
        }
        cities_data.append(city_data)
    
    # 处理直辖市特殊情况：为直辖市添加城市记录
    direct_municipalities = ['11', '12', '31', '50']  # 北京、天津、上海、重庆
    provinces_df = df[df['层级'] == 1]
    
    for _, province_row in provinces_df.iterrows():
        province_code = str(province_row['字典项值']).zfill(2)
        if province_code in direct_municipalities:
            # 为直辖市添加一个同名的城市记录
            city_data = {
                'code': province_code + '01',  # 例如：1101, 1201, 3101, 5001
                'name': province_row['字典项名称'],
                'province_code': province_code
            }
            cities_data.append(city_data)
    
    logger.info("城市数据提取完成", 
               total_level2=len(cities_df),
               filtered_count=len(filtered_cities),
               final_count=len(cities_data))
    return cities_data


def clear_existing_data(session) -> bool:
    """清除现有的省市数据"""
    logger.info("开始清除现有省市数据")
    
    try:
        # 删除城市数据
        city_count = session.query(City).count()
        if city_count > 0:
            session.query(City).delete()
            logger.info("删除现有城市数据", count=city_count)
        
        # 删除省份数据
        province_count = session.query(Province).count()
        if province_count > 0:
            session.query(Province).delete()
            logger.info("删除现有省份数据", count=province_count)
        
        session.commit()
        logger.info("现有数据清除完成")
        return True
        
    except Exception as e:
        session.rollback()
        logger.error("清除现有数据失败", error=str(e))
        return False


def insert_provinces(session, provinces_data: List[Dict]) -> bool:
    """插入省份数据"""
    logger.info("开始插入省份数据")
    
    try:
        current_time = to_utc_for_db()
        inserted_count = 0
        
        for province_info in provinces_data:
            province = Province(
                uuid=uuid.uuid4(),
                code=province_info["code"],
                name=province_info["name"],
                is_active=True,
                is_deleted=False,
                created_at=current_time,
                updated_at=current_time
            )
            session.add(province)
            inserted_count += 1
        
        session.commit()
        logger.info("省份数据插入完成", count=inserted_count)
        return True
        
    except Exception as e:
        session.rollback()
        logger.error("插入省份数据失败", error=str(e))
        return False


def insert_cities(session, cities_data: List[Dict]) -> bool:
    """插入城市数据"""
    logger.info("开始插入城市数据")
    
    try:
        current_time = to_utc_for_db()
        inserted_count = 0
        
        for city_info in cities_data:
            city = City(
                uuid=uuid.uuid4(),
                code=city_info["code"],
                name=city_info["name"],
                province_code=city_info["province_code"],
                is_active=True,
                is_deleted=False,
                created_at=current_time,
                updated_at=current_time
            )
            session.add(city)
            inserted_count += 1
        
        session.commit()
        logger.info("城市数据插入完成", count=inserted_count)
        return True
        
    except Exception as e:
        session.rollback()
        logger.error("插入城市数据失败", error=str(e))
        return False


def verify_data(session) -> bool:
    """验证导入的数据"""
    logger.info("开始验证导入数据")
    
    try:
        # 统计省份数据
        province_count = session.query(Province).filter(Province.is_deleted == False).count()
        logger.info("省份数据统计", count=province_count)
        
        # 统计城市数据
        city_count = session.query(City).filter(City.is_deleted == False).count()
        logger.info("城市数据统计", count=city_count)
        
        # 检查数据关联性
        orphan_cities = session.query(City).filter(
            ~City.province_code.in_(
                session.query(Province.code).filter(Province.is_deleted == False)
            ),
            City.is_deleted == False
        ).count()
        
        if orphan_cities > 0:
            logger.warning("发现孤立城市", count=orphan_cities)
            return False
        
        # 显示部分数据样例
        provinces = session.query(Province).filter(Province.is_deleted == False).limit(5).all()
        logger.info("省份数据样例")
        for province in provinces:
            cities_in_province = session.query(City).filter(
                City.province_code == province.code,
                City.is_deleted == False
            ).count()
            logger.info("省份详情", 
                       name=province.name, 
                       code=province.code, 
                       cities_count=cities_in_province)
        
        logger.info("数据验证完成", 
                   provinces=province_count, 
                   cities=city_count, 
                   orphan_cities=orphan_cities)
        return True
        
    except Exception as e:
        logger.error("数据验证失败", error=str(e))
        return False


def main():
    """主函数"""
    print("🚀 开始从Excel导入省市数据...")
    print("=" * 60)
    
    excel_path = "五级行政区划字典.xlsx"
    
    # 检查Excel文件是否存在
    if not Path(excel_path).exists():
        logger.error("Excel文件不存在", path=excel_path)
        return False
    
    try:
        # 读取Excel数据
        df = read_excel_data(excel_path)
        
        # 提取省市数据
        provinces_data = extract_provinces_data(df)
        cities_data = extract_cities_data(df)
        
        # 创建数据库会话
        session_local = sessionmaker(bind=engine)
        session = session_local()
        
        try:
            # 清除现有数据
            if not clear_existing_data(session):
                return False
            
            # 插入新数据
            if not insert_provinces(session, provinces_data):
                return False
            
            if not insert_cities(session, cities_data):
                return False
            
            # 验证数据
            if not verify_data(session):
                return False
            
            print("\n" + "=" * 60)
            print("🎉 Excel省市数据导入完成！")
            print("=" * 60)
            print(f"✅ 省份: {len(provinces_data)} 个")
            print(f"✅ 城市: {len(cities_data)} 个")
            print("✅ 数据来源: 五级行政区划字典.xlsx")
            print("✅ 数据已成功导入数据库")
            
            return True
            
        finally:
            session.close()
            
    except Exception as e:
        logger.error("导入过程失败", error=str(e))
        return False


if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Excel省市数据导入失败！")
        sys.exit(1)
