version: '3.8'

services:
  fastapi-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fastapi-postgres-container
    ports:
      - "8000:8000"
    environment:
      # 应用配置
      - APP_NAME=汽车街API
      - APP_VERSION=1.0.0
      - APP_DESCRIPTION=智能销售助手
      - DEBUG=false
      - ENVIRONMENT=production
      
      # 服务器配置
      - HOST=0.0.0.0
      - PORT=8000
      - RELOAD=false
      
      # 数据库配置（容器内 PostgreSQL）
      - DATABASE_URL=postgresql://fastapi_user:fastapi_password@localhost:5432/fastapi_db
      - DATABASE_HOST=localhost
      - DATABASE_PORT=5432
      - DATABASE_NAME=fastapi_db
      - DATABASE_USER=fastapi_user
      - DATABASE_PASSWORD=fastapi_password
      
      # 安全配置
      - SECRET_KEY=your-super-secret-key-change-this-in-production-docker
      - ALGORITHM=HS256
      - ACCESS_TOKEN_EXPIRE_MINUTES=1440
      
      # 日志配置
      - LOG_LEVEL=INFO
      - LOG_FORMAT=json
      - LOG_FILE_ENABLED=true
      - LOG_CONSOLE_ENABLED=false
      
      # CORS 配置
      - CORS_ORIGINS=["*"]
      - CORS_ALLOW_CREDENTIALS=true
      - CORS_ALLOW_METHODS=["*"]
      - CORS_ALLOW_HEADERS=["*"]
      
      # API 配置
      - API_V1_PREFIX=/api/v1
      - DOCS_URL=/docs
      - REDOC_URL=/redoc
      
      # 监控配置
      - METRICS_ENABLED=true
      - HEALTH_CHECK_ENABLED=true
    
    volumes:
      # 数据持久化
      - postgres_data:/var/lib/postgresql/14/main
      # 日志持久化
      - app_logs:/app/logs
      # 上传文件持久化
      - app_uploads:/app/uploads
    
    restart: unless-stopped
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    
    networks:
      - fastapi-network

volumes:
  postgres_data:
    driver: local
  app_logs:
    driver: local
  app_uploads:
    driver: local

networks:
  fastapi-network:
    driver: bridge
