[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor

[program:postgresql]
user=postgres
command=/usr/lib/postgresql/13/bin/postgres -D /var/lib/postgresql/data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/postgresql.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
priority=100

[program:fastapi]
command=python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
directory=/app
user=root
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/supervisor/fastapi.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
environment=PYTHONPATH="/app",DATABASE_URL="postgresql://fastapi_user:7EbCi8ekZD5hbbHr@localhost:5432/qichejie"
priority=200
startsecs=10
startretries=3
