# Docker 容器环境配置文件
# 用于 Docker 容器内的环境变量配置

# 应用配置
APP_NAME=汽车街API
APP_VERSION=1.0.0
APP_DESCRIPTION=智能销售助手
DEBUG=false
ENVIRONMENT=production

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=false

# 数据库配置（容器内 PostgreSQL）
DATABASE_URL=postgresql://fastapi_user:7EbCi8ekZD5hbbHr@localhost:5432/qichejie
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=qichejie
DATABASE_USER=fastapi_user
DATABASE_PASSWORD=7EbCi8ekZD5hbbHr

# 安全配置（生产环境请修改）
SECRET_KEY=qgUwUn82ysRhtWfiqidokxd8Xg8GCTq4LFJtsQD3W9FcVKyVMFVfMzJovCLkJeFp
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_CONSOLE_ENABLED=false
LOG_FILE_PATH=logs/fastapi_server.log
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_BACKUP_COUNT=5

# CORS 配置（允许所有来源，生产环境请限制）
CORS_ORIGINS=["*"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# API 文档配置
API_V1_PREFIX=/api/v1
DOCS_URL=/docs
REDOC_URL=/redoc

# 监控配置
METRICS_ENABLED=true
HEALTH_CHECK_ENABLED=true

# 文件上传配置
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_DIR=uploads

# 短信服务配置（预留）
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key
SMS_SIGN_NAME=your-app-name
SMS_TEMPLATE_CODE=SMS_123456789
