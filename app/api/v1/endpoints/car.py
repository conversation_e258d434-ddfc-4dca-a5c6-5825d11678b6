"""
汽车相关的 API 端点
"""

import json
import uuid
import asyncio
import httpx
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, Request, HTTPException, UploadFile, File, Form, Path
from fastapi.responses import StreamingResponse
import structlog

from app.schemas.chat import ASRResponse
from app.schemas.response import create_success_response, SuccessResponse
from app.services.chat_service import ChatService
from app.dependencies import get_current_request_id, require_authentication
from app.utils.logger import BusinessLogger

router = APIRouter()
chat_service = ChatService()
logger = BusinessLogger("car_api")
struct_logger = structlog.get_logger("car_api")


@router.post(
    "/asr",
    response_model=SuccessResponse[ASRResponse],
    tags=["汽车服务"],
    summary="语音识别接口",
    description="上传音频文件进行语音识别，返回识别结果"
)
async def asr_recognition(
    input_file_type: str = Form(..., description="输入文件类型，如wav"),
    file: UploadFile = File(..., description="音频文件"),
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    语音识别接口

    上传音频文件，调用外部ASR服务进行语音识别，返回识别的文本结果。

    Args:
        input_file_type: 输入文件类型（如wav）
        file: 上传的音频文件
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        ASRResponse: 包含语音识别结果的响应

    Raises:
        HTTPException: 当文件格式不支持、文件过大或识别失败时抛出
    """
    try:
        authenticated_user_id = current_user.get("user_id")

        struct_logger.info(
            "开始处理ASR请求",
            user_id=authenticated_user_id,
            input_file_type=input_file_type,
            filename=file.filename,
            content_type=file.content_type,
            request_id=request_id
        )
        
        # 验证文件类型
        if input_file_type.lower() not in ['wav', 'mp3', 'flac', 'm4a']:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件类型: {input_file_type}"
            )
        
        # 读取文件内容
        try:
            file_content = await file.read()
        except Exception as e:
            struct_logger.error(
                "读取上传文件失败",
                error=str(e),
                filename=file.filename,
                request_id=request_id
            )
            raise HTTPException(
                status_code=400,
                detail="文件读取失败"
            )
        
        # 验证文件大小（限制为50MB）
        max_file_size = 50 * 1024 * 1024  # 50MB
        if len(file_content) > max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"文件过大，最大支持{max_file_size // (1024*1024)}MB"
            )
        
        # 调用外部ASR服务
        external_api_url = "http://*************:9527/v1/apis/asr"
        # authorization_header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

        headers = {
            
        }

        # 准备multipart/form-data
        files = {
            'file': (file.filename or "audio.wav", file_content, file.content_type or 'audio/wav')
        }

        data = {
            'user_id': str(authenticated_user_id),
            'input_file_type': 'wav'  # 固定为wav
        }

        struct_logger.info(
            "开始调用外部ASR服务",
            external_url=external_api_url,
            user_id=authenticated_user_id,
            input_file_type='wav',
            filename=file.filename,
            file_size=len(file_content),
            request_id=request_id
        )

        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    external_api_url,
                    headers=headers,
                    files=files,
                    data=data
                )

                struct_logger.info(
                    "外部ASR服务调用完成",
                    status_code=response.status_code,
                    user_id=authenticated_user_id,
                    request_id=request_id
                )

                if response.status_code != 200:
                    struct_logger.error(
                        "外部ASR服务调用失败",
                        status_code=response.status_code,
                        response_text=response.text,
                        user_id=authenticated_user_id,
                        request_id=request_id
                    )
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"ASR服务调用失败: {response.text}"
                    )

                # 尝试解析JSON响应，如果失败则返回原始文本
                try:
                    asr_result = response.json()
                except json.JSONDecodeError:
                    asr_result = response.text

        except httpx.TimeoutException:
            struct_logger.error(
                "外部ASR服务调用超时",
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=504,
                detail="ASR服务调用超时"
            )
        except httpx.RequestError as e:
            struct_logger.error(
                "外部ASR服务调用网络错误",
                error=str(e),
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=502,
                detail="ASR服务连接失败"
            )
        
        struct_logger.info(
            "ASR处理成功",
            user_id=authenticated_user_id,
            filename=file.filename,
            result_size=len(str(asr_result)),
            request_id=request_id
        )
        
        return create_success_response(
            data=ASRResponse(data=asr_result),
            message="语音识别成功",
            request_id=request_id
        )
        
    except HTTPException:
        raise
    except Exception as e:
        struct_logger.error(
            "ASR处理失败",
            user_id=authenticated_user_id,
            filename=file.filename if file else "unknown",
            error=str(e),
            request_id=request_id
        )
        raise HTTPException(
            status_code=500,
            detail="语音识别服务异常"
        )


@router.get(
    "/vehicle_cars/{uuid}/pictures",
    tags=["汽车服务"],
    summary="获取车辆图片",
    description="根据车辆UUID获取车辆图片信息"
)
async def get_vehicle_pictures(
    uuid: str = Path(..., description="车辆UUID"),
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取车辆图片接口

    根据车辆UUID查询对应的真实车辆ID，然后调用外部API获取车辆图片信息。

    Args:
        uuid: 车辆UUID
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        外部API的原始响应数据

    Raises:
        HTTPException: 当UUID无效、车辆不存在或外部API调用失败时抛出
    """
    try:
        authenticated_user_id = current_user.get("user_id")

        struct_logger.info(
            "开始处理车辆图片请求",
            uuid=uuid,
            user_id=authenticated_user_id,
            request_id=request_id
        )

        # 验证UUID格式
        try:
            # 尝试解析UUID以验证格式
            import uuid as uuid_module
            uuid_module.UUID(uuid)
        except ValueError:
            struct_logger.warning(
                "无效的UUID格式",
                uuid=uuid,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=400,
                detail="无效的UUID格式"
            )

        # 通过UUID获取真实车辆ID
        real_car_id = chat_service.get_real_id_by_car_uuid(uuid)

        if not real_car_id:
            struct_logger.warning(
                "车辆UUID不存在",
                uuid=uuid,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=404,
                detail="车辆不存在"
            )

        struct_logger.info(
            "找到对应的真实车辆ID",
            uuid=uuid,
            real_car_id=real_car_id,
            user_id=authenticated_user_id,
            request_id=request_id
        )

        # 调用外部API
        external_api_url = "http://*************:9527"
        external_endpoint = f"/v1/cars/{real_car_id}/pictures"
        # authorization_header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

        headers = {
            # "Authorization": authorization_header
        }

        struct_logger.info(
            "开始调用外部API",
            external_url=f"{external_api_url}{external_endpoint}",
            real_car_id=real_car_id,
            user_id=authenticated_user_id,
            request_id=request_id
        )

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{external_api_url}{external_endpoint}",
                    headers=headers
                )

                struct_logger.info(
                    "外部API调用完成",
                    status_code=response.status_code,
                    real_car_id=real_car_id,
                    user_id=authenticated_user_id,
                    request_id=request_id
                )

                # 检查响应状态
                if response.status_code != 200:
                    struct_logger.error(
                        "外部API调用失败",
                        status_code=response.status_code,
                        response_text=response.text,
                        real_car_id=real_car_id,
                        user_id=authenticated_user_id,
                        request_id=request_id
                    )
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"外部服务调用失败: {response.text}"
                    )

                # 尝试解析JSON响应，如果失败则返回原始文本
                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    response_data = response.text

                struct_logger.info(
                    "车辆图片获取成功",
                    uuid=uuid,
                    real_car_id=real_car_id,
                    user_id=authenticated_user_id,
                    response_size=len(str(response_data)),
                    request_id=request_id
                )

                # 原封不动返回外部API响应
                return response_data

        except httpx.TimeoutException:
            struct_logger.error(
                "外部API调用超时",
                real_car_id=real_car_id,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=504,
                detail="外部服务调用超时"
            )
        except httpx.RequestError as e:
            struct_logger.error(
                "外部API调用网络错误",
                error=str(e),
                real_car_id=real_car_id,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=502,
                detail="外部服务连接失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        struct_logger.error(
            "车辆图片获取失败",
            uuid=uuid,
            error=str(e),
            user_id=authenticated_user_id,
            request_id=request_id
        )
        raise HTTPException(
            status_code=500,
            detail="车辆图片服务异常"
        )


@router.get(
    "/vehicle_cars/{uuid}/damage",
    tags=["汽车服务"],
    summary="获取车辆损伤信息",
    description="根据车辆UUID获取车辆损伤信息"
)
async def get_vehicle_damage(
    uuid: str = Path(..., description="车辆UUID"),
    current_user: Dict[str, Any] = Depends(require_authentication),
    request_id: str = Depends(get_current_request_id)
):
    """
    获取车辆损伤信息接口

    根据车辆UUID查询对应的真实车辆ID，然后调用外部API获取车辆损伤信息。

    Args:
        uuid: 车辆UUID
        current_user: 当前认证用户信息
        request_id: 请求ID，用于日志追踪

    Returns:
        外部API的原始响应数据

    Raises:
        HTTPException: 当UUID无效、车辆不存在或外部API调用失败时抛出
    """
    try:
        authenticated_user_id = current_user.get("user_id")

        struct_logger.info(
            "开始处理车辆损伤信息请求",
            uuid=uuid,
            user_id=authenticated_user_id,
            request_id=request_id
        )

        # 验证UUID格式
        try:
            # 尝试解析UUID以验证格式
            import uuid as uuid_module
            uuid_module.UUID(uuid)
        except ValueError:
            struct_logger.warning(
                "无效的UUID格式",
                uuid=uuid,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=400,
                detail="无效的UUID格式"
            )

        # 通过UUID获取真实车辆ID
        real_car_id = chat_service.get_real_id_by_car_uuid(uuid)

        if not real_car_id:
            struct_logger.warning(
                "车辆UUID不存在",
                uuid=uuid,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=404,
                detail="车辆不存在"
            )

        struct_logger.info(
            "找到对应的真实车辆ID",
            uuid=uuid,
            real_car_id=real_car_id,
            user_id=authenticated_user_id,
            request_id=request_id
        )

        # 调用外部API
        external_api_url = "http://*************:9527"
        external_endpoint = f"/v1/cars/{real_car_id}/damage"
        # authorization_header = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

        headers = {
            # "Authorization": authorization_header
        }

        struct_logger.info(
            "开始调用外部API",
            external_url=f"{external_api_url}{external_endpoint}",
            real_car_id=real_car_id,
            user_id=authenticated_user_id,
            request_id=request_id
        )

        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(
                    f"{external_api_url}{external_endpoint}",
                    headers=headers
                )

                struct_logger.info(
                    "外部API调用完成",
                    status_code=response.status_code,
                    real_car_id=real_car_id,
                    user_id=authenticated_user_id,
                    request_id=request_id
                )

                # 检查响应状态
                if response.status_code != 200:
                    struct_logger.error(
                        "外部API调用失败",
                        status_code=response.status_code,
                        response_text=response.text,
                        real_car_id=real_car_id,
                        user_id=authenticated_user_id,
                        request_id=request_id
                    )
                    raise HTTPException(
                        status_code=response.status_code,
                        detail=f"外部服务调用失败: {response.text}"
                    )

                # 尝试解析JSON响应，如果失败则返回原始文本
                try:
                    response_data = response.json()
                except json.JSONDecodeError:
                    response_data = response.text

                struct_logger.info(
                    "车辆损伤信息获取成功",
                    uuid=uuid,
                    real_car_id=real_car_id,
                    user_id=authenticated_user_id,
                    response_size=len(str(response_data)),
                    request_id=request_id
                )

                # 原封不动返回外部API响应
                return response_data

        except httpx.TimeoutException:
            struct_logger.error(
                "外部API调用超时",
                real_car_id=real_car_id,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=504,
                detail="外部服务调用超时"
            )
        except httpx.RequestError as e:
            struct_logger.error(
                "外部API调用网络错误",
                error=str(e),
                real_car_id=real_car_id,
                user_id=authenticated_user_id,
                request_id=request_id
            )
            raise HTTPException(
                status_code=502,
                detail="外部服务连接失败"
            )

    except HTTPException:
        raise
    except Exception as e:
        struct_logger.error(
            "车辆损伤信息获取失败",
            uuid=uuid,
            error=str(e),
            user_id=authenticated_user_id,
            request_id=request_id
        )
        raise HTTPException(
            status_code=500,
            detail="车辆损伤服务异常"
        )
