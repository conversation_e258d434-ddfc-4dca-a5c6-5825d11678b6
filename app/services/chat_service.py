"""
聊天服务
"""

import json
import uuid
import asyncio
from typing import List, Dict, Any, Optional, AsyncGenerator
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc, or_
import structlog
import httpx
from datetime import datetime

from app.models.chat import ChatMessage, CarRecommendation, CarComparison, CarIdMapping
from app.models.conversation import AIConversation
from app.models.user import User
from app.schemas.chat import ChatCompletionRequest, ConversationHistoryItem, ChatMessageInfo, CarRecommendationInfo
from app.database import SessionLocal
from app.core.exceptions import BusinessLogicError, ValidationError
from app.utils.logger import BusinessLogger
from app.utils.timezone import get_china_now
from app.services.cache_service import conversation_cache


class ChatService:
    """聊天服务"""

    def __init__(self):
        self.logger = BusinessLogger("chat_service")
        self.struct_logger = structlog.get_logger("chat_service")

    def save_user_message(self, request_data: ChatCompletionRequest, user_id: int) -> ChatMessage:
        """保存用户消息"""
        db = SessionLocal()
        try:
            # 验证对话是否存在且属于用户
            conversation = db.query(AIConversation).filter(
                and_(
                    AIConversation.uuid == request_data.conversation_id,
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).first()

            if not conversation:
                raise BusinessLogicError("对话不存在或不属于当前用户")

            # 创建用户消息
            user_message = ChatMessage(
                conversation_id=request_data.conversation_id,
                user_id=user_id,
                message_type="user",
                content=request_data.message,
                is_complete=True
            )
            
            db.add(user_message)
            db.commit()
            db.refresh(user_message)
            
            self.struct_logger.info(
                "用户消息保存成功",
                message_id=str(user_message.uuid),
                conversation_id=request_data.conversation_id,
                user_id=user_id
            )
            
            return user_message
            
        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "保存用户消息失败",
                conversation_id=request_data.conversation_id,
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def create_assistant_message(self, conversation_id: str, user_id: int, message_id: str) -> ChatMessage:
        """创建AI助手消息"""
        db = SessionLocal()
        try:
            assistant_message = ChatMessage(
                conversation_id=conversation_id,
                user_id=user_id,
                message_type="assistant",
                content="",
                message_id=message_id,
                is_complete=False
            )
            
            db.add(assistant_message)
            db.commit()
            db.refresh(assistant_message)
            
            return assistant_message
            
        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "创建AI消息失败",
                conversation_id=conversation_id,
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def update_assistant_message(self, message_uuid: str, content: str, is_complete: bool = False):
        """更新AI助手消息内容"""
        db = SessionLocal()
        try:
            # 添加调试日志
            self.struct_logger.info(
                "开始更新AI消息内容",
                message_uuid=str(message_uuid),
                content_length=len(content),
                content_preview=content[:200] if len(content) > 200 else content,
                is_complete=is_complete
            )
            
            message = db.query(ChatMessage).filter(ChatMessage.uuid == message_uuid).first()
            if message:
                message.content = content
                message.is_complete = is_complete
                db.commit()
                
                self.struct_logger.info(
                    "AI消息内容更新成功",
                    message_uuid=str(message_uuid),
                    content_length=len(content),
                    is_complete=is_complete
                )
            else:
                self.struct_logger.warning(
                    "未找到要更新的AI消息",
                    message_uuid=str(message_uuid)
                )
                
        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "更新AI消息失败",
                message_uuid=str(message_uuid),
                error=str(e)
            )
        finally:
            db.close()

    def get_or_create_car_mapping(self, real_car_id: str) -> CarIdMapping:
        """获取或创建车辆ID映射记录"""
        # 验证车辆ID格式，确保不是UUID
        if not self._is_real_car_id(real_car_id):
            self.struct_logger.warning(
                "尝试为UUID格式的ID创建映射",
                car_id=real_car_id
            )
            raise ValueError(f"无效的车辆ID格式: {real_car_id}")

        db = SessionLocal()
        try:
            # 首先尝试查找现有映射
            existing_mapping = db.query(CarIdMapping).filter(
                and_(
                    CarIdMapping.real_car_id == real_car_id,
                    CarIdMapping.is_deleted == False
                )
            ).first()

            if existing_mapping:
                # 更新使用统计
                existing_mapping.update_usage()
                db.commit()
                db.refresh(existing_mapping)

                self.struct_logger.info(
                    "使用现有车辆ID映射",
                    real_car_id=real_car_id,
                    car_uuid=str(existing_mapping.car_uuid),
                    usage_count=existing_mapping.usage_count
                )
                return existing_mapping

            # 创建新的映射记录
            new_mapping = CarIdMapping(
                real_car_id=real_car_id,
                car_uuid=uuid.uuid4()
            )

            db.add(new_mapping)
            db.commit()
            db.refresh(new_mapping)

            self.struct_logger.info(
                "创建新车辆ID映射",
                real_car_id=real_car_id,
                car_uuid=str(new_mapping.car_uuid)
            )
            return new_mapping

        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "获取或创建车辆ID映射失败",
                real_car_id=real_car_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def get_car_uuid_by_real_id(self, real_car_id: str) -> Optional[str]:
        """通过真实车辆ID获取对应的UUID"""
        db = SessionLocal()
        try:
            mapping = db.query(CarIdMapping).filter(
                and_(
                    CarIdMapping.real_car_id == real_car_id,
                    CarIdMapping.is_deleted == False
                )
            ).first()

            if mapping:
                return str(mapping.car_uuid)
            return None

        except Exception as e:
            self.struct_logger.error(
                "通过真实ID查找UUID失败",
                real_car_id=real_car_id,
                error=str(e)
            )
            return None
        finally:
            db.close()

    def get_real_id_by_car_uuid(self, car_uuid: str) -> Optional[str]:
        """通过车辆UUID获取对应的真实ID"""
        db = SessionLocal()
        try:
            mapping = db.query(CarIdMapping).filter(
                and_(
                    CarIdMapping.car_uuid == car_uuid,
                    CarIdMapping.is_deleted == False
                )
            ).first()

            if mapping:
                return mapping.real_car_id
            return None

        except Exception as e:
            self.struct_logger.error(
                "通过UUID查找真实ID失败",
                car_uuid=car_uuid,
                error=str(e)
            )
            return None
        finally:
            db.close()

    def batch_get_or_create_car_mappings(self, real_car_ids: List[str]) -> Dict[str, str]:
        """批量获取或创建车辆ID映射，返回真实ID到UUID的映射字典"""
        # 过滤出有效的车辆ID，排除UUID格式
        valid_car_ids = []
        for car_id in real_car_ids:
            if self._is_real_car_id(car_id):
                valid_car_ids.append(car_id)
            else:
                self.struct_logger.warning(
                    "跳过UUID格式的车辆ID",
                    car_id=car_id
                )

        if not valid_car_ids:
            return {}

        db = SessionLocal()
        try:
            result_mappings = {}

            # 批量查询现有映射
            existing_mappings = db.query(CarIdMapping).filter(
                and_(
                    CarIdMapping.real_car_id.in_(valid_car_ids),
                    CarIdMapping.is_deleted == False
                )
            ).all()

            # 构建现有映射字典
            existing_dict = {mapping.real_car_id: mapping for mapping in existing_mappings}

            # 更新现有映射的使用统计
            for mapping in existing_mappings:
                mapping.update_usage()
                result_mappings[mapping.real_car_id] = str(mapping.car_uuid)

            # 找出需要创建新映射的车辆ID
            missing_ids = set(valid_car_ids) - set(existing_dict.keys())

            # 批量创建新映射
            new_mappings = []
            for real_car_id in missing_ids:
                new_mapping = CarIdMapping(
                    real_car_id=real_car_id,
                    car_uuid=uuid.uuid4()
                )
                new_mappings.append(new_mapping)
                result_mappings[real_car_id] = str(new_mapping.car_uuid)

            if new_mappings:
                db.add_all(new_mappings)

            db.commit()

            self.struct_logger.info(
                "批量处理车辆ID映射完成",
                total_count=len(valid_car_ids),
                filtered_count=len(real_car_ids) - len(valid_car_ids),
                existing_count=len(existing_mappings),
                new_count=len(new_mappings)
            )

            return result_mappings

        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "批量处理车辆ID映射失败",
                real_car_ids=real_car_ids,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def save_car_recommendation(self, conversation_id: str, message_id: str,
                              chat_message_uuid: str, recommendation_data: Dict[str, Any]):
        """保存车辆推荐数据"""
        db = SessionLocal()
        try:
            self.struct_logger.info(
                "开始保存车辆推荐数据",
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid,
                data_type=type(recommendation_data).__name__,
                data_keys=list(recommendation_data.keys()) if isinstance(recommendation_data, dict) else None,
                data_length=len(recommendation_data) if isinstance(recommendation_data, (list, dict, str)) else None,
                data_preview=str(recommendation_data)[:500] if recommendation_data else "None"
            )

            # 解析推荐数据
            if isinstance(recommendation_data, str):
                try:
                    recommendation_data = json.loads(recommendation_data)
                    self.struct_logger.info("成功解析字符串为JSON", parsed_type=type(recommendation_data).__name__)
                except json.JSONDecodeError as e:
                    self.struct_logger.error("推荐数据JSON解析失败", data=recommendation_data, error=str(e))
                    return

            # 提取车辆ID和任务类型
            car_id = None
            car_uuid = None
            task_type = "unknown"
            slots_data = {}
            processed_data = None

            # 处理不同类型的推荐数据
            if isinstance(recommendation_data, list) and len(recommendation_data) > 0:
                # 车辆数组数据：[{"id": 1534654399, "brand": "奥迪", ...}]
                self.struct_logger.info(
                    "处理车辆数组数据",
                    array_length=len(recommendation_data),
                    first_item_type=type(recommendation_data[0]).__name__,
                    first_item_keys=list(recommendation_data[0].keys()) if isinstance(recommendation_data[0], dict) else None
                )
                
                first_item = recommendation_data[0]
                if isinstance(first_item, dict) and "id" in first_item:
                    # 提取所有车辆的真实ID
                    real_car_ids = []
                    for car_data in recommendation_data:
                        if isinstance(car_data, dict) and "id" in car_data:
                            real_car_ids.append(str(car_data["id"]))

                    self.struct_logger.info(
                        "提取到车辆ID列表",
                        real_car_ids=real_car_ids,
                        count=len(real_car_ids)
                    )

                    # 批量获取或创建ID映射
                    id_mappings = self.batch_get_or_create_car_mappings(real_car_ids)

                    # 创建处理后的数据副本
                    processed_data = []
                    for car_data in recommendation_data:
                        if isinstance(car_data, dict) and "id" in car_data:
                            original_id = str(car_data["id"])

                            # 使用映射表中的UUID
                            mapped_uuid = id_mappings.get(original_id)
                            if mapped_uuid:
                                # 创建副本并替换ID
                                car_copy = car_data.copy()
                                car_copy["id"] = mapped_uuid
                                processed_data.append(car_copy)

                                # 第一个车辆的ID作为主要car_id和car_uuid
                                if car_id is None:
                                    car_id = original_id
                                    car_uuid = mapped_uuid
                            else:
                                # 如果映射失败，保留原始数据
                                self.struct_logger.warning(
                                    "车辆ID映射失败",
                                    original_id=original_id
                                )
                                processed_data.append(car_data)
                        else:
                            processed_data.append(car_data)

                    task_type = "recommend_car"

                    # 记录ID映射
                    self.struct_logger.info(
                        "车辆ID映射完成（数组）",
                        id_mappings=id_mappings,
                        processed_count=len(processed_data),
                        conversation_id=conversation_id,
                        message_id=message_id
                    )

            elif isinstance(recommendation_data, dict):
                self.struct_logger.info(
                    "处理字典类型数据",
                    dict_keys=list(recommendation_data.keys()),
                    has_active_task_type="active_task_type" in recommendation_data,
                    has_id="id" in recommendation_data
                )
                
                if "active_task_type" in recommendation_data:
                    # 任务类型数据：{"active_task_type": "recommend_car", "slots": {...}}
                    # 这种数据不包含具体车辆，不需要车辆ID映射，直接保存任务信息
                    task_type = recommendation_data.get("active_task_type", "unknown")
                    slots_data = recommendation_data.get("slots", {})

                    self.struct_logger.info(
                        "处理任务类型数据，跳过车辆ID映射",
                        task_type=task_type,
                        slots_data=slots_data
                    )

                    # 任务类型数据不需要车辆ID，直接保存推荐记录
                    recommendation_record = CarRecommendation(
                        conversation_id=uuid.UUID(conversation_id),
                        message_id=uuid.UUID(message_id),
                        chat_message_uuid=uuid.UUID(chat_message_uuid),
                        car_uuid=None,  # 任务类型数据没有具体车辆
                        recommendation_data=recommendation_data,
                        task_data=task_data
                    )

                    db.add(recommendation_record)

                    self.struct_logger.info(
                        "任务类型推荐记录已保存",
                        task_type=task_type,
                        conversation_id=conversation_id,
                        message_id=message_id
                    )

                    # 任务类型数据处理完成，直接返回
                    db.commit()
                    return

                elif "id" in recommendation_data:
                    # 单个车辆数据：{"id": 1534654399, "brand": "奥迪", ...}
                    car_id = str(recommendation_data["id"])
                    
                    self.struct_logger.info(
                        "处理单个车辆数据",
                        original_car_id=car_id,
                        is_real_id=self._is_real_car_id(car_id)
                    )

                    # 检查是否为真实车辆ID
                    if self._is_real_car_id(car_id):
                        # 使用映射表获取或创建UUID
                        mapping = self.get_or_create_car_mapping(car_id)
                        car_uuid = str(mapping.car_uuid)
                    else:
                        # 如果已经是UUID，直接使用
                        car_uuid = car_id
                        # 尝试通过UUID查找真实ID
                        real_id = self.get_real_id_by_car_uuid(car_id)
                        if real_id:
                            car_id = real_id
                        else:
                            # 如果找不到对应的真实ID，生成一个占位符
                            car_id = f"placeholder_{uuid.uuid4().hex[:8]}"
                            self.struct_logger.warning(
                                "无法找到UUID对应的真实车辆ID，使用占位符",
                                car_uuid=car_uuid,
                                placeholder_id=car_id
                            )

                    # 创建处理后的数据副本
                    processed_data = recommendation_data.copy()
                    processed_data["id"] = car_uuid  # 替换为UUID

                    task_type = "recommend_car"

                    # 记录ID映射
                    self.struct_logger.info(
                        "车辆ID映射完成（单个）",
                        id_mapping={car_id: car_uuid},
                        conversation_id=conversation_id,
                        message_id=message_id
                    )
                else:
                    # 字典数据但没有id字段，可能是其他类型的数据
                    self.struct_logger.warning(
                        "字典数据缺少id字段",
                        dict_keys=list(recommendation_data.keys())
                    )

            # 如果没有找到车辆ID或car_uuid，生成一个占位符ID和UUID
            if not car_id or not car_uuid:
                if not car_id:
                    car_id = f"unknown_{uuid.uuid4().hex[:8]}"
                # 检查car_id是否为真实ID格式
                if self._is_real_car_id(car_id):
                    # 为占位符ID创建映射
                    mapping = self.get_or_create_car_mapping(car_id)
                    car_uuid = str(mapping.car_uuid)
                else:
                    # 如果car_id已经是UUID格式，直接使用
                    car_uuid = car_id
                    car_id = f"placeholder_{uuid.uuid4().hex[:8]}"

                self.struct_logger.info(
                    "生成占位符车辆ID和UUID",
                    car_id=car_id,
                    car_uuid=car_uuid,
                    conversation_id=conversation_id,
                    message_id=message_id
                )

            # 如果没有处理后的数据，使用原始数据
            if processed_data is None:
                processed_data = recommendation_data
                self.struct_logger.warning(
                    "使用原始数据作为处理后的数据",
                    data_type=type(recommendation_data).__name__
                )

            # 验证必要字段
            if not car_id or not car_uuid:
                error_msg = f"缺少必要字段: car_id={car_id}, car_uuid={car_uuid}"
                self.struct_logger.error(error_msg)
                raise ValueError(error_msg)

            # 创建推荐记录
            recommendation = CarRecommendation(
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid,
                car_id=car_id,
                car_uuid=car_uuid,  # 添加car_uuid字段
                task_type=task_type,
                recommendation_data=processed_data,  # 保存处理后的数据
                slots_data=slots_data
            )

            self.struct_logger.info(
                "准备保存推荐记录到数据库",
                car_id=car_id,
                car_uuid=car_uuid,
                task_type=task_type,
                slots_data=slots_data,
                processed_data_type=type(processed_data).__name__,
                processed_data_length=len(processed_data) if isinstance(processed_data, (list, dict, str)) else None,
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid
            )

            db.add(recommendation)
            db.commit()
            db.refresh(recommendation)

            self.struct_logger.info(
                "车辆推荐保存成功",
                recommendation_id=str(recommendation.uuid),
                recommendation_db_id=recommendation.id,
                car_id=car_id,
                car_uuid=car_uuid,
                conversation_id=conversation_id,
                task_type=task_type,
                created_at=recommendation.created_at.isoformat() if recommendation.created_at else None
            )
            
        except Exception as e:
            db.rollback()
            import traceback
            self.struct_logger.error(
                "保存车辆推荐失败",
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid,
                error=str(e),
                error_type=type(e).__name__,
                traceback=traceback.format_exc(),
                recommendation_data_type=type(recommendation_data).__name__ if recommendation_data else None,
                recommendation_data_preview=str(recommendation_data)[:500] if recommendation_data else None
            )
            raise
        finally:
            db.close()

    async def get_complete_ai_response(self, message: str, conversation_id: str, user_id: str, message_id: str) -> Dict[str, Any]:
        """获取完整的AI服务响应并处理推荐数据"""

        ai_base_url = "hhttp://*************:9527"
        ai_endpoint = "/v1/chat/completions"
        # ai_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

        headers = {
            # "Authorization": ai_token,
            "Content-Type": "application/json"
        }

        payload = {
            "message": message,
            "conversation_id": conversation_id,
            "user_id": user_id
        }

        accumulated_content = ""
        car_data_list = []  # 收集所有车辆数据
        task_data = None
        has_finish_reason = False
        
        # ANSI 颜色代码
        YELLOW = '\033[93m'
        RESET = '\033[0m'
        
        # 收集完整响应用于日志
        full_response = ""

        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream(
                    "POST",
                    f"{ai_base_url}{ai_endpoint}",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status_code != 200:
                        self.struct_logger.error(
                            "AI服务请求失败",
                            status_code=response.status_code,
                            response_text=await response.aread()
                        )
                        return {
                            "success": False,
                            "error": f"AI服务请求失败，状态码: {response.status_code}",
                            "content": "抱歉，AI服务暂时不可用，请稍后重试。"
                        }

                    # 收集所有响应数据
                    async for chunk in response.aiter_text():
                        # 收集完整响应
                        full_response += chunk
                        if chunk:
                            # 处理可能包含多行的chunk
                            lines = chunk.strip().split('\n')
                            for line in lines:
                                line = line.strip()
                                if not line:
                                    continue
                                    
                                # 解析响应数据
                                if line.startswith("data: "):
                                    data_content = line[6:].strip()

                                    if data_content == "[DONE]":
                                        break

                                    try:
                                        # 解析JSON数据
                                        response_data = json.loads(data_content)

                                        if "data" in response_data and "choices" in response_data["data"]:
                                            choices = response_data["data"]["choices"]
                                            if choices and len(choices) > 0:
                                                choice = choices[0]
                                                delta = choice.get("delta", "")
                                                finish_reason = choice.get("finish_reason")

                                            # 检查是否有结束标记
                                            if finish_reason == "stop":
                                                has_finish_reason = True
                                                self.struct_logger.info(
                                                    "AI服务返回完成标记",
                                                    finish_reason=finish_reason,
                                                    conversation_id=conversation_id,
                                                    message_id=message_id
                                                )
                                                # 不要跳过，因为同一个chunk可能包含delta内容

                                            # 处理delta内容
                                            if delta and isinstance(delta, str):
                                                # 添加调试日志
                                                self.struct_logger.debug(
                                                    "处理delta内容",
                                                    delta=delta,
                                                    delta_length=len(delta),
                                                    accumulated_length_before=len(accumulated_content),
                                                    conversation_id=conversation_id,
                                                    message_id=message_id
                                                )
                                                
                                                # 只有当delta看起来像是完整的JSON结构时才尝试解析
                                                # 检查是否包含JSON的特征（以{或[开头，以}或]结尾）
                                                delta_stripped = delta.strip()
                                                is_json_structure = ((delta_stripped.startswith('{') and delta_stripped.endswith('}')) or
                                                                   (delta_stripped.startswith('[') and delta_stripped.endswith(']')))
                                                
                                                if is_json_structure:
                                                    try:
                                                        # 尝试解析为JSON，可能是推荐数据
                                                        recommendation_data = json.loads(delta)
                                                        is_special_data = False
                                                        
                                                        if isinstance(recommendation_data, list) and len(recommendation_data) > 0:
                                                            # 检查是否是车辆数据数组
                                                            first_item = recommendation_data[0]
                                                            if isinstance(first_item, dict) and "id" in first_item:
                                                                # 车辆数据数组 - 添加到车辆列表，不添加到文本内容
                                                                car_data_list.extend(recommendation_data)
                                                                is_special_data = True
                                                                self.struct_logger.info(
                                                                    "收到车辆数据",
                                                                    car_count=len(recommendation_data),
                                                                    total_cars=len(car_data_list),
                                                                    conversation_id=conversation_id,
                                                                    message_id=message_id
                                                                )
                                                        elif isinstance(recommendation_data, dict) and "active_task_type" in recommendation_data:
                                                            # 任务类型数据 - 不添加到文本内容
                                                            task_data = recommendation_data
                                                            is_special_data = True
                                                            self.struct_logger.info(
                                                                "收到任务类型数据",
                                                                task_type=recommendation_data.get("active_task_type"),
                                                                slots=recommendation_data.get("slots", {}),
                                                                conversation_id=conversation_id,
                                                                message_id=message_id
                                                            )
                                                        
                                                        # 只有非特殊数据才添加到文本内容
                                                        if not is_special_data:
                                                            accumulated_content += delta
                                                            self.struct_logger.debug(
                                                                "JSON数据作为文本添加",
                                                                delta=delta,
                                                                accumulated_length_after=len(accumulated_content),
                                                                conversation_id=conversation_id,
                                                                message_id=message_id
                                                            )
                                                        else:
                                                            self.struct_logger.debug(
                                                                "跳过特殊JSON数据",
                                                                delta_preview=delta[:100] if len(delta) > 100 else delta,
                                                                is_car_data=len(car_data_list) > 0,
                                                                is_task_data=task_data is not None,
                                                                conversation_id=conversation_id,
                                                                message_id=message_id
                                                            )
                                                    except json.JSONDecodeError:
                                                        # 解析失败，作为普通文本处理
                                                        accumulated_content += delta
                                                        self.struct_logger.debug(
                                                            "JSON解析失败，作为文本添加",
                                                            delta=delta,
                                                            accumulated_length_after=len(accumulated_content),
                                                            conversation_id=conversation_id,
                                                            message_id=message_id
                                                        )
                                                else:
                                                    # 不是JSON结构，直接作为普通文本
                                                    accumulated_content += delta
                                                    self.struct_logger.debug(
                                                        "普通文本添加",
                                                        delta=delta,
                                                        accumulated_length_after=len(accumulated_content),
                                                        conversation_id=conversation_id,
                                                        message_id=message_id
                                                    )
                                            elif delta:
                                                # 普通增量文本
                                                accumulated_content += delta

                                    except json.JSONDecodeError:
                                        self.struct_logger.warning("解析响应数据失败", data=data_content)
            
            # 输出完整的响应内容（黄色）
            print(f"\n{YELLOW}{'='*80}")
            print(f"[AI服务完整响应 - get_complete_ai_response] conversation_id: {conversation_id}, message_id: {message_id}")
            print(f"{'='*80}")
            print(full_response)
            print(f"{'='*80}{RESET}\n")
            
            # 同时记录到结构化日志
            self.struct_logger.info(
                "AI服务完整响应 - get_complete_ai_response",
                conversation_id=conversation_id,
                message_id=message_id,
                response_length=len(full_response),
                full_response=full_response
            )

        except Exception as e:
            self.struct_logger.error(
                "调用AI服务异常",
                error=str(e),
                message=message,
                conversation_id=conversation_id
            )
            return {
                "success": False,
                "error": f"AI服务异常: {str(e)}",
                "content": "抱歉，AI服务暂时不可用，请稍后重试。"
            }

        # 根据任务类型和车辆数据组织推荐数据
        recommendations = []
        if car_data_list:
            if task_data and task_data.get("active_task_type") == "compare_car":
                # 车辆对比：所有车辆作为一个对比组
                recommendations.append(car_data_list)
            else:
                # 单车推荐：每辆车单独推荐
                for car_data in car_data_list:
                    recommendations.append([car_data])

        self.struct_logger.info(
            "AI响应处理完成",
            content_length=len(accumulated_content),
            car_count=len(car_data_list),
            recommendation_count=len(recommendations),
            task_type=task_data.get("active_task_type") if task_data else None,
            conversation_id=conversation_id,
            message_id=message_id
        )

        return {
            "success": True,
            "content": accumulated_content,
            "recommendations": recommendations,
            "task_data": task_data
        }

    async def call_ai_service(self, message: str, conversation_id: str, user_id: str, message_id: str) -> AsyncGenerator[str, None]:
        """调用真实AI服务并直接转发响应"""

        ai_base_url = "http://*************:9527"
        ai_endpoint = "/v1/chat/completions"
        # ai_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

        headers = {
            # "Authorization": ai_token,
            "Content-Type": "application/json"
        }

        payload = {
            "message": message,
            "conversation_id": conversation_id,
            "user_id": user_id
        }

        # ANSI 颜色代码
        YELLOW = '\033[93m'
        RESET = '\033[0m'

        # 用于存储车辆ID到UUID的映射
        car_id_to_uuid_map = {}

        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                async with client.stream(
                    "POST",
                    f"{ai_base_url}{ai_endpoint}",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status_code != 200:
                        self.struct_logger.error(
                            "AI服务请求失败",
                            status_code=response.status_code,
                            response_text=await response.aread()
                        )
                        # 返回错误响应
                        error_response = {
                            "data": {
                                "user_id": user_id,
                                "message_id": message_id,
                                "conversation_id": conversation_id,
                                "choices": [{"delta": "抱歉，AI服务暂时不可用，请稍后重试。"}],
                                "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                            },
                            "status": {"code": response.status_code, "message": "AI Service Error"}
                        }
                        yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
                        yield "data: [DONE]\n\n"
                        return

                    # 缓冲区，用于处理不完整的行
                    buffer = ""
                    # 收集所有响应内容用于完整日志
                    full_response = ""
                    
                    # 直接转发AI服务的流式响应
                    async for chunk in response.aiter_text():
                        # 只跳过完全空的chunk，保留包含空白字符的chunk
                        if chunk:
                            # 收集完整响应
                            full_response += chunk
                            
                            self.struct_logger.debug(
                                "收到AI服务chunk",
                                chunk_length=len(chunk),
                                chunk_preview=chunk[:100] if len(chunk) > 100 else chunk,
                                conversation_id=conversation_id,
                                message_id=message_id
                            )
                            
                            # 将新的chunk添加到缓冲区
                            buffer += chunk
                            
                            # 处理缓冲区中的完整行
                            while '\n' in buffer:
                                # 找到第一个换行符的位置
                                line_end = buffer.index('\n')
                                # 提取完整的行（包括换行符）
                                line = buffer[:line_end + 1]
                                # 从缓冲区中移除已处理的行
                                buffer = buffer[line_end + 1:]
                                
                                # 处理数据行，替换车辆ID
                                processed_line = self._process_streaming_line(line, car_id_to_uuid_map)
                                
                                # 发送处理后的行
                                yield processed_line
                                
                                self.struct_logger.debug(
                                    "发送完整行",
                                    line_length=len(processed_line),
                                    line_preview=processed_line[:100] if len(processed_line) > 100 else processed_line.strip(),
                                    buffer_remaining=len(buffer),
                                    conversation_id=conversation_id,
                                    message_id=message_id
                                )
                        else:
                            self.struct_logger.debug(
                                "跳过空chunk",
                                conversation_id=conversation_id,
                                message_id=message_id
                            )
                    
                    # 处理缓冲区中剩余的内容
                    if buffer:
                        self.struct_logger.warning(
                            "缓冲区中有未完成的内容",
                            buffer_content=buffer,
                            buffer_length=len(buffer),
                            conversation_id=conversation_id,
                            message_id=message_id
                        )
                        # 如果缓冲区中还有内容，确保它以换行符结尾
                        if not buffer.endswith('\n'):
                            buffer += '\n'
                        # 处理剩余内容
                        processed_buffer = self._process_streaming_line(buffer, car_id_to_uuid_map)
                        yield processed_buffer
                        # 添加到完整响应
                        full_response += buffer
                    
                    # 输出完整的响应内容（黄色）
                    print(f"\n{YELLOW}{'='*80}")
                    print(f"[AI服务完整响应] conversation_id: {conversation_id}, message_id: {message_id}")
                    print(f"{'='*80}")
                    print(full_response)
                    print(f"{'='*80}{RESET}\n")
                    
                    # 同时记录到结构化日志
                    self.struct_logger.info(
                        "AI服务完整响应",
                        conversation_id=conversation_id,
                        message_id=message_id,
                        response_length=len(full_response),
                        full_response=full_response
                    )

        except Exception as e:
            self.struct_logger.error(
                "调用AI服务异常",
                error=str(e),
                message=message,
                conversation_id=conversation_id
            )
            # 返回异常响应
            error_response = {
                "data": {
                    "user_id": user_id,
                    "message_id": message_id,
                    "conversation_id": conversation_id,
                    "choices": [{"delta": "抱歉，AI服务暂时不可用，请稍后重试。"}],
                    "usage": {"prompt_tokens": 0, "completion_tokens": 0, "total_tokens": 0}
                },
                "status": {"code": 500, "message": "Internal Server Error"}
            }
            yield f"data: {json.dumps(error_response, ensure_ascii=False)}\n\n"
            yield "data: [DONE]\n\n"

    def save_car_comparison(self, conversation_id: str, message_id: str,
                          chat_message_uuid: str, comparison_data: List[Dict[str, Any]],
                          slots_data: Dict[str, Any]):
        """保存车辆对比数据"""
        db = SessionLocal()
        try:
            self.struct_logger.info(
                "开始保存车辆对比数据",
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid,
                data_count=len(comparison_data) if comparison_data else 0,
                slots_data=slots_data
            )
            
            if len(comparison_data) < 2:
                self.struct_logger.warning("对比数据不足两辆车", data_count=len(comparison_data))
                return

            # 验证车辆数据并提取车辆ID
            if not isinstance(comparison_data[0], dict) or "id" not in comparison_data[0]:
                self.struct_logger.error("第一辆车数据无效或缺少ID字段", car_data=comparison_data[0])
                return

            if not isinstance(comparison_data[1], dict) or "id" not in comparison_data[1]:
                self.struct_logger.error("第二辆车数据无效或缺少ID字段", car_data=comparison_data[1])
                return

            # 提取车辆ID
            original_id_1 = str(comparison_data[0]["id"])
            original_id_2 = str(comparison_data[1]["id"])
            
            self.struct_logger.info(
                "提取到的车辆ID",
                original_id_1=original_id_1,
                original_id_2=original_id_2,
                is_real_id_1=self._is_real_car_id(original_id_1),
                is_real_id_2=self._is_real_car_id(original_id_2)
            )

            # 处理第一辆车的ID
            if self._is_real_car_id(original_id_1):
                # 真实车辆ID，使用映射表获取或创建UUID
                mapping_1 = self.get_or_create_car_mapping(original_id_1)
                car_id_1 = original_id_1
                car_uuid_1 = mapping_1.car_uuid
            else:
                # 已经是UUID格式
                car_uuid_1 = uuid.UUID(original_id_1)  # 验证UUID格式
                # 尝试通过UUID查找真实ID
                real_id_1 = self.get_real_id_by_car_uuid(original_id_1)
                if real_id_1:
                    car_id_1 = real_id_1
                else:
                    # 如果找不到对应的真实ID，生成一个占位符
                    car_id_1 = f"placeholder_{uuid.uuid4().hex[:8]}"
                    self.struct_logger.warning(
                        "无法找到UUID对应的真实车辆ID，使用占位符",
                        car_uuid=original_id_1,
                        placeholder_id=car_id_1
                    )

            # 处理第二辆车的ID
            if self._is_real_car_id(original_id_2):
                # 真实车辆ID，使用映射表获取或创建UUID
                mapping_2 = self.get_or_create_car_mapping(original_id_2)
                car_id_2 = original_id_2
                car_uuid_2 = mapping_2.car_uuid
            else:
                # 已经是UUID格式
                car_uuid_2 = uuid.UUID(original_id_2)  # 验证UUID格式
                # 尝试通过UUID查找真实ID
                real_id_2 = self.get_real_id_by_car_uuid(original_id_2)
                if real_id_2:
                    car_id_2 = real_id_2
                else:
                    # 如果找不到对应的真实ID，生成一个占位符
                    car_id_2 = f"placeholder_{uuid.uuid4().hex[:8]}"
                    self.struct_logger.warning(
                        "无法找到UUID对应的真实车辆ID，使用占位符",
                        car_uuid=original_id_2,
                        placeholder_id=car_id_2
                    )

            # 替换原始数据中的ID为UUID（用于对外展示）
            processed_data = []
            for i, car_data in enumerate(comparison_data):
                car_copy = car_data.copy()
                if i == 0:
                    car_copy["id"] = str(car_uuid_1)
                else:
                    car_copy["id"] = str(car_uuid_2)
                processed_data.append(car_copy)

            # 创建对比记录
            comparison = CarComparison(
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid,
                car_id_1=car_id_1,
                car_id_2=car_id_2,
                car_uuid_1=car_uuid_1,
                car_uuid_2=car_uuid_2,
                task_type="compare_car",
                comparison_data=processed_data,
                slots_data=slots_data
            )

            self.struct_logger.info(
                "准备保存对比记录到数据库",
                car_id_1=car_id_1,
                car_id_2=car_id_2,
                car_uuid_1=str(car_uuid_1),
                car_uuid_2=str(car_uuid_2),
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid
            )

            db.add(comparison)
            db.commit()
            db.refresh(comparison)

            self.struct_logger.info(
                "车辆对比保存成功",
                comparison_id=str(comparison.uuid),
                comparison_db_id=comparison.id,
                conversation_id=conversation_id,
                car_count=len(comparison_data),
                car_id_mappings={
                    "car_1": {"real_id": car_id_1, "uuid": str(car_uuid_1)},
                    "car_2": {"real_id": car_id_2, "uuid": str(car_uuid_2)}
                },
                created_at=comparison.created_at.isoformat() if comparison.created_at else None
            )

        except Exception as e:
            db.rollback()
            import traceback
            self.struct_logger.error(
                "保存车辆对比失败",
                conversation_id=conversation_id,
                message_id=message_id,
                chat_message_uuid=chat_message_uuid,
                error=str(e),
                error_type=type(e).__name__,
                traceback=traceback.format_exc(),
                comparison_data_preview=str(comparison_data)[:500] if comparison_data else None
            )
            raise
        finally:
            db.close()

    def get_conversation_history(self, conversation_id: str, user_id: int,
                               page: int = 1, limit: int = 50, use_cache: bool = True) -> List[ConversationHistoryItem]:
        """获取对话历史（优化版本，支持缓存和批量查询）"""

        # 尝试从缓存获取
        if use_cache:
            cached_history = conversation_cache.get_conversation_history(
                conversation_id, user_id, page, limit
            )
            if cached_history:
                return [ConversationHistoryItem(**item) for item in cached_history]

        db = SessionLocal()
        try:
            # 验证对话是否存在且属于用户
            conversation = db.query(AIConversation).filter(
                and_(
                    AIConversation.uuid == conversation_id,
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).first()

            if not conversation:
                raise BusinessLogicError("对话不存在或不属于当前用户")

            # 计算分页偏移量
            offset = (page - 1) * limit

            # 优化查询：使用预加载和批量查询
            messages = db.query(ChatMessage).filter(
                and_(
                    ChatMessage.conversation_id == conversation_id,
                    ChatMessage.is_deleted == False
                )
            ).order_by(ChatMessage.created_at.asc()).offset(offset).limit(limit).all()

            if not messages:
                return []

            # 批量查询所有相关的推荐和对比数据
            message_uuids = [msg.uuid for msg in messages]

            # 批量查询推荐数据
            recommendations = db.query(CarRecommendation).filter(
                and_(
                    CarRecommendation.chat_message_uuid.in_(message_uuids),
                    CarRecommendation.is_deleted == False
                )
            ).all()

            # 批量查询对比数据
            comparisons = db.query(CarComparison).filter(
                and_(
                    CarComparison.chat_message_uuid.in_(message_uuids),
                    CarComparison.is_deleted == False
                )
            ).all()

            # 按消息UUID组织推荐和对比数据
            recommendations_by_msg = {}
            for rec in recommendations:
                msg_uuid = rec.chat_message_uuid
                if msg_uuid not in recommendations_by_msg:
                    recommendations_by_msg[msg_uuid] = []
                recommendations_by_msg[msg_uuid].append(rec)

            comparisons_by_msg = {}
            for comp in comparisons:
                msg_uuid = comp.chat_message_uuid
                if msg_uuid not in comparisons_by_msg:
                    comparisons_by_msg[msg_uuid] = []
                comparisons_by_msg[msg_uuid].append(comp)

            # 构建历史项
            history_items = []
            for message in messages:
                # 获取该消息的推荐数据
                msg_recommendations = recommendations_by_msg.get(message.uuid, [])
                recommendation_infos = []
                for rec in msg_recommendations:
                    recommendation_infos.append(CarRecommendationInfo(
                        id=str(rec.uuid),
                        conversation_id=str(rec.conversation_id),
                        message_id=rec.message_id,
                        task_type=rec.task_type,
                        recommendation_data=rec.recommendation_data,
                        slots_data=rec.slots_data,
                        created_at=rec.created_at.isoformat() if rec.created_at else None
                    ))

                # 获取该消息的对比数据（扩展功能）
                msg_comparisons = comparisons_by_msg.get(message.uuid, [])
                comparison_infos = []
                for comp in msg_comparisons:
                    comparison_infos.append({
                        "id": str(comp.uuid),
                        "conversation_id": str(comp.conversation_id),
                        "message_id": comp.message_id,
                        "task_type": comp.task_type,
                        "car_1_id": str(comp.car_uuid_1),
                        "car_2_id": str(comp.car_uuid_2),
                        "comparison_data": comp.comparison_data,
                        "slots_data": comp.slots_data,
                        "created_at": comp.created_at.isoformat() if comp.created_at else None
                    })

                # 创建历史项
                history_item = ConversationHistoryItem(
                    message=ChatMessageInfo(
                        id=message.id,
                        message_id=str(message.uuid),
                        conversation_id=str(message.conversation_id),
                        user_id=message.user_id,
                        message_type=message.message_type,
                        content=message.content,
                        is_complete=message.is_complete,
                        created_at=message.created_at.isoformat() if message.created_at else None,
                        updated_at=message.updated_at.isoformat() if message.updated_at else None
                    ),
                    recommendations=recommendation_infos,
                    comparisons=comparison_infos  # 新增对比数据
                )

                history_items.append(history_item)

            # 缓存结果
            if use_cache and history_items:
                # 转换为可序列化的格式进行缓存
                cacheable_data = []
                for item in history_items:
                    cacheable_data.append({
                        "message": item.message.dict(),
                        "recommendations": [rec.dict() for rec in item.recommendations],
                        "comparisons": item.comparisons if hasattr(item, 'comparisons') else []
                    })

                conversation_cache.set_conversation_history(
                    conversation_id, user_id, cacheable_data, page, limit
                )

            self.struct_logger.info(
                "获取对话历史成功",
                conversation_id=conversation_id,
                user_id=user_id,
                page=page,
                limit=limit,
                message_count=len(history_items),
                from_cache=False
            )

            return history_items

        except Exception as e:
            self.struct_logger.error(
                "获取对话历史失败",
                conversation_id=conversation_id,
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    async def generate_conversation_title(self, conversation_id: str, message: str, user_id: int) -> str:
        """生成对话标题"""
        db = SessionLocal()
        try:
            # 验证对话是否存在且属于当前用户
            conversation = db.query(AIConversation).filter(
                and_(
                    AIConversation.uuid == conversation_id,
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).first()

            if not conversation:
                raise BusinessLogicError("对话不存在或无权限访问")

            # 调用外部API生成标题
            ai_base_url = "http://*************:9527"
            ai_endpoint = "/v1/chat/summary_topics"
            # ai_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

            headers = {
                # "Authorization": ai_token,
                "Content-Type": "application/json"
            }

            payload = {
                "conversation_id": conversation_id,
                "message": message
            }

            try:
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.post(
                        f"{ai_base_url}{ai_endpoint}",
                        headers=headers,
                        json=payload
                    )

                    if response.status_code != 200:
                        self.struct_logger.error(
                            "AI话题摘要服务请求失败",
                            status_code=response.status_code,
                            response_text=response.text
                        )
                        raise BusinessLogicError("话题摘要生成失败")

                    response_data = response.json()

                    # 检查响应格式
                    if response_data.get("status") != "success":
                        self.struct_logger.error(
                            "AI话题摘要服务返回错误",
                            response_data=response_data
                        )
                        raise BusinessLogicError("话题摘要生成失败")

                    # 获取生成的标题
                    generated_title = response_data.get("data", "新对话")

                    # 更新对话标题
                    conversation.title = generated_title
                    conversation.updated_at = get_china_now()

                    db.commit()
                    db.refresh(conversation)

                    self.struct_logger.info(
                        "对话标题生成成功",
                        conversation_id=conversation_id,
                        old_title=conversation.title,
                        new_title=generated_title
                    )

                    return generated_title

            except httpx.RequestError as e:
                self.struct_logger.error(
                    "调用AI话题摘要服务网络异常",
                    error=str(e),
                    conversation_id=conversation_id
                )
                raise BusinessLogicError("网络连接异常，请稍后重试")

        except BusinessLogicError:
            db.rollback()
            raise
        except Exception as e:
            db.rollback()
            self.struct_logger.error(
                "生成对话标题失败",
                conversation_id=conversation_id,
                user_id=user_id,
                error=str(e)
            )
            raise BusinessLogicError("生成对话标题失败")
        finally:
            db.close()

    async def process_asr_request(self, user_id: int, input_file_type: str, file_content: bytes, filename: str) -> Dict[str, Any]:
        """处理ASR语音识别请求"""
        try:
            # 调用外部ASR API
            ai_base_url = "http://*************:9527"
            ai_endpoint = "/v1/apis/asr"
            # ai_token = "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJ3YnU4cWJaVm5sWm8yNHkzOWEyMlF4bEhxcjBRcStaU0VGMXpsZz09IiwiZXhwIjoxNzUzNjA1MDA0LCJuYmYiOjE3NTEwMTI4MjR9.kIE53jYOY7eCFjw38nEwOAqBBlqA1TYTDOiUopAZU7M"

            headers = {
                # "Authorization": ai_token
            }

            # 准备multipart/form-data
            files = {
                'file': (filename, file_content, 'audio/wav')
            }

            data = {
                'user_id': str(user_id),
                'input_file_type': input_file_type
            }

            self.struct_logger.info(
                "开始调用ASR服务",
                user_id=user_id,
                input_file_type=input_file_type,
                filename=filename,
                file_size=len(file_content)
            )

            try:
                async with httpx.AsyncClient(timeout=60.0) as client:
                    response = await client.post(
                        f"{ai_base_url}{ai_endpoint}",
                        headers=headers,
                        files=files,
                        data=data
                    )

                    if response.status_code != 200:
                        self.struct_logger.error(
                            "ASR服务请求失败",
                            status_code=response.status_code,
                            response_text=response.text
                        )
                        raise BusinessLogicError(f"ASR服务请求失败，状态码: {response.status_code}")

                    # 解析响应
                    try:
                        response_data = response.json()
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，返回原始文本
                        response_data = {"text": response.text}

                    self.struct_logger.info(
                        "ASR服务调用成功",
                        user_id=user_id,
                        response_size=len(response.text)
                    )

                    return response_data

            except httpx.RequestError as e:
                self.struct_logger.error(
                    "调用ASR服务网络异常",
                    error=str(e),
                    user_id=user_id
                )
                raise BusinessLogicError("网络连接异常，请稍后重试")

        except BusinessLogicError:
            raise
        except Exception as e:
            self.struct_logger.error(
                "ASR服务处理失败",
                user_id=user_id,
                error=str(e)
            )
            raise BusinessLogicError("ASR服务处理失败")

    def _is_real_car_id(self, car_id: str) -> bool:
        """判断是否为真实车辆ID（非UUID格式）"""
        # 真实车辆ID通常是数字格式
        # UUID格式包含连字符且长度较长
        if "-" in car_id and len(car_id) > 20:
            return False  # 这是UUID格式

        # 检查是否为纯数字或数字字符串
        return car_id.isdigit() or (car_id.replace('.', '').isdigit())

    def _process_streaming_line(self, line: str, car_id_to_uuid_map: Dict[str, str]) -> str:
        """处理流式响应行，替换车辆ID为UUID"""
        try:
            # 只处理以 "data: " 开头的行
            if not line.startswith("data: "):
                return line
            
            data_content = line[6:].strip()
            
            # 跳过特殊标记
            if data_content == "[DONE]" or not data_content:
                return line
            
            try:
                # 解析JSON数据
                response_data = json.loads(data_content)
                
                # 检查是否包含choices和delta
                if "data" in response_data and "choices" in response_data["data"]:
                    choices = response_data["data"]["choices"]
                    if choices and len(choices) > 0:
                        choice = choices[0]
                        delta = choice.get("delta", "")
                        
                        # 如果delta是字符串且看起来像JSON
                        if delta and isinstance(delta, str):
                            delta_stripped = delta.strip()
                            
                            # 检查是否是JSON结构
                            is_json_structure = ((delta_stripped.startswith('{') and delta_stripped.endswith('}')) or
                                               (delta_stripped.startswith('[') and delta_stripped.endswith(']')))
                            
                            if is_json_structure:
                                try:
                                    # 尝试解析delta内容
                                    delta_data = json.loads(delta)
                                    
                                    # 处理车辆数据数组
                                    if isinstance(delta_data, list) and len(delta_data) > 0:
                                        first_item = delta_data[0]
                                        if isinstance(first_item, dict) and "id" in first_item:
                                            # 这是车辆数据，需要替换ID
                                            processed_data = []
                                            real_car_ids = []

                                            # 收集所有真实车辆ID
                                            for car_data in delta_data:
                                                if isinstance(car_data, dict) and "id" in car_data:
                                                    original_id = str(car_data["id"])
                                                    # 只处理真实车辆ID（数字格式），跳过已经是UUID的
                                                    if self._is_real_car_id(original_id):
                                                        real_car_ids.append(original_id)

                                            # 批量获取或创建映射
                                            if real_car_ids:
                                                id_mappings = self.batch_get_or_create_car_mappings(real_car_ids)
                                                car_id_to_uuid_map.update(id_mappings)

                                            # 处理车辆数据
                                            for car_data in delta_data:
                                                if isinstance(car_data, dict) and "id" in car_data:
                                                    original_id = str(car_data["id"])

                                                    # 创建副本并替换ID
                                                    car_copy = car_data.copy()
                                                    if original_id in car_id_to_uuid_map:
                                                        car_copy["id"] = car_id_to_uuid_map[original_id]
                                                    # 如果已经是UUID，保持不变
                                                    processed_data.append(car_copy)
                                                else:
                                                    processed_data.append(car_data)

                                            # 更新delta内容
                                            choice["delta"] = json.dumps(processed_data, ensure_ascii=False)

                                            self.struct_logger.info(
                                                "流式响应中替换车辆ID（使用映射表）",
                                                original_ids=real_car_ids,
                                                car_count=len(processed_data)
                                            )
                                    
                                    # 处理单个车辆数据
                                    elif isinstance(delta_data, dict) and "id" in delta_data:
                                        original_id = str(delta_data["id"])

                                        # 只处理真实车辆ID，跳过已经是UUID的
                                        if self._is_real_car_id(original_id):
                                            # 获取或创建映射
                                            if original_id not in car_id_to_uuid_map:
                                                mapping = self.get_or_create_car_mapping(original_id)
                                                car_id_to_uuid_map[original_id] = str(mapping.car_uuid)

                                            # 创建副本并替换ID
                                            processed_data = delta_data.copy()
                                            processed_data["id"] = car_id_to_uuid_map[original_id]

                                            # 更新delta内容
                                            choice["delta"] = json.dumps(processed_data, ensure_ascii=False)

                                            self.struct_logger.info(
                                                "流式响应中替换单个车辆ID（使用映射表）",
                                                original_id=original_id,
                                                new_id=car_id_to_uuid_map[original_id]
                                            )
                                
                                except json.JSONDecodeError:
                                    # delta内容不是有效的JSON，保持原样
                                    pass
                
                # 重新构建响应行
                return f"data: {json.dumps(response_data, ensure_ascii=False)}\n"
                
            except json.JSONDecodeError:
                # 整行不是有效的JSON，返回原始行
                return line
                
        except Exception as e:
            self.struct_logger.error(
                "处理流式响应行失败",
                error=str(e),
                line=line
            )
            # 出错时返回原始行
            return line

    def invalidate_conversation_cache(self, conversation_id: str, user_id: Optional[int] = None) -> int:
        """使对话缓存失效"""
        try:
            invalidated_count = conversation_cache.invalidate_conversation(conversation_id, user_id)

            self.struct_logger.info(
                "对话缓存已失效",
                conversation_id=conversation_id,
                user_id=user_id,
                invalidated_count=invalidated_count
            )

            return invalidated_count

        except Exception as e:
            self.struct_logger.error(
                "使对话缓存失效失败",
                conversation_id=conversation_id,
                user_id=user_id,
                error=str(e)
            )
            return 0

    def get_conversation_summary(self, conversation_id: str, user_id: int) -> Dict[str, Any]:
        """获取对话摘要信息"""
        db = SessionLocal()
        try:
            # 验证对话是否存在且属于用户
            conversation = db.query(AIConversation).filter(
                and_(
                    AIConversation.uuid == conversation_id,
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False
                )
            ).first()

            if not conversation:
                raise BusinessLogicError("对话不存在或不属于当前用户")

            # 统计消息数量
            message_count = db.query(ChatMessage).filter(
                and_(
                    ChatMessage.conversation_id == conversation_id,
                    ChatMessage.is_deleted == False
                )
            ).count()

            # 统计推荐数量
            recommendation_count = db.query(CarRecommendation).filter(
                and_(
                    CarRecommendation.conversation_id == conversation_id,
                    CarRecommendation.is_deleted == False
                )
            ).count()

            # 统计对比数量
            comparison_count = db.query(CarComparison).filter(
                and_(
                    CarComparison.conversation_id == conversation_id,
                    CarComparison.is_deleted == False
                )
            ).count()

            # 获取最后一条消息
            last_message = db.query(ChatMessage).filter(
                and_(
                    ChatMessage.conversation_id == conversation_id,
                    ChatMessage.is_deleted == False
                )
            ).order_by(ChatMessage.created_at.desc()).first()

            summary = {
                "conversation_id": str(conversation.uuid),
                "title": conversation.title,
                "message_count": message_count,
                "recommendation_count": recommendation_count,
                "comparison_count": comparison_count,
                "last_message_at": last_message.created_at.isoformat() if last_message and last_message.created_at else None,
                "last_message_type": last_message.message_type if last_message else None,
                "created_at": conversation.created_at.isoformat() if conversation.created_at else None,
                "updated_at": conversation.updated_at.isoformat() if conversation.updated_at else None
            }

            self.struct_logger.info(
                "获取对话摘要成功",
                conversation_id=conversation_id,
                user_id=user_id,
                summary=summary
            )

            return summary

        except Exception as e:
            self.struct_logger.error(
                "获取对话摘要失败",
                conversation_id=conversation_id,
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()

    def get_recent_recommendations(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """获取用户最近的推荐记录"""
        db = SessionLocal()
        try:
            # 查询用户最近的推荐记录
            recommendations = db.query(CarRecommendation).join(
                AIConversation, CarRecommendation.conversation_id == AIConversation.uuid
            ).filter(
                and_(
                    AIConversation.user_id == user_id,
                    AIConversation.is_deleted == False,
                    CarRecommendation.is_deleted == False
                )
            ).order_by(CarRecommendation.created_at.desc()).limit(limit).all()

            recent_recs = []
            for rec in recommendations:
                recent_recs.append({
                    "id": str(rec.uuid),
                    "conversation_id": str(rec.conversation_id),
                    "message_id": rec.message_id,
                    "task_type": rec.task_type,
                    "recommendation_data": rec.recommendation_data,
                    "created_at": rec.created_at.isoformat() if rec.created_at else None
                })

            self.struct_logger.info(
                "获取最近推荐成功",
                user_id=user_id,
                limit=limit,
                count=len(recent_recs)
            )

            return recent_recs

        except Exception as e:
            self.struct_logger.error(
                "获取最近推荐失败",
                user_id=user_id,
                error=str(e)
            )
            raise
        finally:
            db.close()
