#!/bin/bash

# Docker 构建和运行脚本
# 用于快速构建和启动 FastAPI + PostgreSQL 单容器应用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "FastAPI + PostgreSQL Docker 管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  build     构建 Docker 镜像"
    echo "  run       运行容器（使用 docker-compose）"
    echo "  stop      停止容器"
    echo "  restart   重启容器"
    echo "  logs      查看容器日志"
    echo "  clean     清理容器和镜像"
    echo "  status    查看容器状态"
    echo "  shell     进入容器 shell"
    echo "  diagnose  网络诊断"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 build && $0 run    # 构建并运行"
    echo "  $0 logs -f            # 实时查看日志"
    echo "  $0 clean              # 清理所有相关资源"
}

# 构建镜像
build_image() {
    log_info "开始构建 Docker 镜像..."

    # 检查 Dockerfile 是否存在
    if [ ! -f "Dockerfile" ]; then
        log_error "Dockerfile 不存在！"
        exit 1
    fi

    # 检查数据库备份文件
    if [ ! -f "aiqcj_2025-07-29_11-45-32_pgsql_data.sql" ]; then
        log_error "数据库备份文件不存在！"
        exit 1
    fi

    # 网络诊断
    log_info "进行网络诊断..."
    if ! ping -c 1 mirrors.aliyun.com > /dev/null 2>&1; then
        log_warn "无法连接到阿里云镜像源，构建可能较慢"
    fi

    # 构建镜像
    log_info "开始构建镜像（这可能需要几分钟）..."
    docker build -t fastapi-postgres:latest . || {
        log_error "镜像构建失败！"
        log_info "请检查网络连接和 Docker 守护进程状态"
        exit 1
    }

    log_info "镜像构建完成！"
    docker images | grep fastapi-postgres
}

# 运行容器
run_container() {
    log_info "启动容器..."
    
    # 检查 docker-compose.yml 是否存在
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml 不存在！"
        exit 1
    fi
    
    # 使用 docker-compose 启动
    docker-compose up -d || {
        log_error "容器启动失败！"
        exit 1
    }
    
    log_info "容器启动成功！"
    log_info "API 地址: http://localhost:8000"
    log_info "API 文档: http://localhost:8000/docs"
    log_info "ReDoc 文档: http://localhost:8000/redoc"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查健康状态
    check_health
}

# 停止容器
stop_container() {
    log_info "停止容器..."
    docker-compose down || {
        log_warn "停止容器时出现警告"
    }
    log_info "容器已停止"
}

# 重启容器
restart_container() {
    log_info "重启容器..."
    stop_container
    sleep 2
    run_container
}

# 查看日志
show_logs() {
    if [ "$1" = "-f" ]; then
        log_info "实时查看容器日志（按 Ctrl+C 退出）..."
        docker-compose logs -f
    else
        log_info "查看容器日志..."
        docker-compose logs --tail=50
    fi
}

# 清理资源
clean_resources() {
    log_warn "这将删除所有相关的容器、镜像和数据卷！"
    read -p "确定要继续吗？(y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理容器..."
        docker-compose down -v --remove-orphans || true
        
        log_info "删除镜像..."
        docker rmi fastapi-postgres:latest || true
        
        log_info "清理未使用的资源..."
        docker system prune -f
        
        log_info "清理完成！"
    else
        log_info "取消清理操作"
    fi
}

# 查看状态
show_status() {
    log_info "容器状态:"
    docker-compose ps
    
    echo ""
    log_info "镜像信息:"
    docker images | grep -E "(fastapi-postgres|REPOSITORY)"
    
    echo ""
    log_info "数据卷信息:"
    docker volume ls | grep -E "(fastapi|postgres)" || echo "没有找到相关数据卷"
}

# 进入容器 shell
enter_shell() {
    log_info "进入容器 shell..."
    docker-compose exec fastapi-app /bin/bash || {
        log_error "无法进入容器，请确保容器正在运行"
        exit 1
    }
}

# 网络诊断
diagnose_network() {
    log_info "开始网络诊断..."

    # 检查基本网络连接
    log_info "检查基本网络连接..."
    if ping -c 3 8.8.8.8 > /dev/null 2>&1; then
        log_info "✅ 基本网络连接正常"
    else
        log_error "❌ 基本网络连接失败"
    fi

    # 检查 DNS 解析
    log_info "检查 DNS 解析..."
    if nslookup mirrors.aliyun.com > /dev/null 2>&1; then
        log_info "✅ DNS 解析正常"
    else
        log_error "❌ DNS 解析失败"
    fi

    # 检查镜像源连接
    log_info "检查镜像源连接..."
    if curl -I https://mirrors.aliyun.com > /dev/null 2>&1; then
        log_info "✅ 阿里云镜像源连接正常"
    else
        log_warn "⚠️  阿里云镜像源连接失败，尝试其他源"
    fi

    # 检查 Docker Hub 连接
    log_info "检查 Docker Hub 连接..."
    if curl -I https://hub.docker.com > /dev/null 2>&1; then
        log_info "✅ Docker Hub 连接正常"
    else
        log_warn "⚠️  Docker Hub 连接失败"
    fi

    log_info "网络诊断完成"
}

# 检查健康状态
check_health() {
    log_info "检查服务健康状态..."

    local max_attempts=30
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8000/health > /dev/null 2>&1; then
            log_info "✅ 服务健康检查通过！"
            return 0
        fi

        log_debug "健康检查中... (尝试 $attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "❌ 服务健康检查失败"
    log_info "查看日志以获取更多信息:"
    docker-compose logs --tail=20
    return 1
}

# 主函数
main() {
    case "${1:-help}" in
        build)
            build_image
            ;;
        run)
            run_container
            ;;
        stop)
            stop_container
            ;;
        restart)
            restart_container
            ;;
        logs)
            show_logs "$2"
            ;;
        clean)
            clean_resources
            ;;
        status)
            show_status
            ;;
        shell)
            enter_shell
            ;;
        diagnose)
            diagnose_network
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    log_error "Docker 未安装或不在 PATH 中"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    log_error "docker-compose 未安装或不在 PATH 中"
    exit 1
fi

# 运行主函数
main "$@"
