#!/bin/bash

# 健康检查脚本
# 检查FastAPI应用和关键API接口的健康状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 基础健康检查
basic_health_check() {
    log_info "执行基础健康检查..."
    
    # 检查基础健康端点
    if curl -f -s http://localhost:8000/health > /dev/null 2>&1; then
        log_info "基础健康检查通过"
        return 0
    else
        log_error "基础健康检查失败"
        return 1
    fi
}

# API接口健康检查
api_health_check() {
    log_info "执行API接口健康检查..."
    
    # 发送POST请求到验证码接口
    local response
    local http_code
    
    response=$(curl -s -w "%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d '{
            "phone": "+8613333333333",
            "purpose": "login"
        }' \
        http://localhost:8000/api/v1/auth/send-code 2>/dev/null)
    
    # 提取HTTP状态码
    http_code="${response: -3}"
    # 提取响应体
    response_body="${response%???}"
    
    log_info "HTTP状态码: $http_code"
    log_info "响应内容: $response_body"
    
    # 检查HTTP状态码
    if [ "$http_code" != "200" ]; then
        log_error "API请求失败，HTTP状态码: $http_code"
        return 1
    fi
    
    # 解析JSON响应
    local success
    local message
    
    # 使用python解析JSON（因为容器中有python）
    success=$(echo "$response_body" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('success', 'false'))
except:
    print('false')
")
    
    message=$(echo "$response_body" | python3 -c "
import sys, json
try:
    data = json.load(sys.stdin)
    print(data.get('message', '解析失败'))
except:
    print('JSON解析失败')
")
    
    # 检查success字段
    if [ "$success" = "True" ] || [ "$success" = "true" ]; then
        log_info "API接口健康检查通过: $message"
        return 0
    else
        log_error "API接口健康检查失败: $message"
        return 1
    fi
}

# 主函数
main() {
    log_info "=== 开始健康检查 ==="
    
    # 执行基础健康检查
    if ! basic_health_check; then
        log_error "基础健康检查失败，退出"
        exit 1
    fi
    
    # 执行API接口健康检查
    if ! api_health_check; then
        log_error "API接口健康检查失败，退出"
        exit 1
    fi
    
    log_info "=== 所有健康检查通过 ==="
    exit 0
}

# 执行主函数
main "$@"
