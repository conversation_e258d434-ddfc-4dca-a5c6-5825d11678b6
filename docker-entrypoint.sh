#!/bin/bash
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 一次性初始化函数
one_time_init() {
    log_info "=== 容器一次性初始化 ==="

    # 设置文件权限
    log_info "设置文件权限..."
    chown -R postgres:postgres /var/lib/postgresql/data
    chmod 700 /var/lib/postgresql/data

    # 创建必要的目录
    mkdir -p /app/logs /app/uploads /app/uploads/avatars
    chmod 755 /app/uploads /app/uploads/avatars
    chown -R root:root /app
    chmod +x /app/run.py

    log_info "一次性初始化完成"
}

# 主函数
main() {
    log_info "=== FastAPI + PostgreSQL 容器启动 ==="
    log_info "容器启动时间: $(date)"

    # 执行一次性初始化
    one_time_init

    log_info "=== 启动服务 ==="

    # 执行传入的命令（通常是supervisord）
    exec "$@"
}

# 如果脚本被直接执行，运行主函数
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
