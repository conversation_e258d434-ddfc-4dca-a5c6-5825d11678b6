# 应用配置
APP_NAME=FastAPI Server
APP_VERSION=1.0.0
APP_DESCRIPTION=基于手机号验证码登录的 FastAPI 服务器
DEBUG=true
ENVIRONMENT=development

# 服务器配置
HOST=0.0.0.0
PORT=8000
RELOAD=true

# 数据库配置（PostgreSQL）
DATABASE_URL=postgresql://username:password@localhost:5432/qichejie
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=qichejie
DATABASE_USER=username
DATABASE_PASSWORD=password

# 安全配置
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=logs/fastapi_server.log
LOG_FILE_MAX_SIZE=10485760
LOG_FILE_BACKUP_COUNT=5

# CORS 配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# API 文档配置
DOCS_URL=/docs
REDOC_URL=/redoc

# 短信服务配置（预留）
SMS_ACCESS_KEY=your-sms-access-key
SMS_SECRET_KEY=your-sms-secret-key
SMS_SIGN_NAME=your-app-name
SMS_TEMPLATE_CODE=SMS_123456789

# 文件上传配置
MAX_FILE_SIZE=5242880  # 5MB
UPLOAD_DIR=uploads
