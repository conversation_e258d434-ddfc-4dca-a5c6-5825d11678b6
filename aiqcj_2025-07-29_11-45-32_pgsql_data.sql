--
-- PostgreSQL database dump
--

-- Dumped from database version 16.1
-- Dumped by pg_dump version 16.1

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

ALTER TABLE ONLY public.user_tokens DROP CONSTRAINT user_tokens_user_id_fkey;
ALTER TABLE ONLY public.chat_messages DROP CONSTRAINT chat_messages_user_id_fkey;
ALTER TABLE ONLY public.chat_messages DROP CONSTRAINT chat_messages_conversation_id_fkey;
ALTER TABLE ONLY public.car_recommendations DROP CONSTRAINT car_recommendations_conversation_id_fkey;
ALTER TABLE ONLY public.car_recommendations DROP CONSTRAINT car_recommendations_chat_message_uuid_fkey;
ALTER TABLE ONLY public.car_comparisons DROP CONSTRAINT car_comparisons_conversation_id_fkey;
ALTER TABLE ONLY public.car_comparisons DROP CONSTRAINT car_comparisons_chat_message_uuid_fkey;
ALTER TABLE ONLY public.ai_conversations DROP CONSTRAINT ai_conversations_user_id_fkey;
DROP INDEX public.ix_users_uuid;
DROP INDEX public.ix_users_phone;
DROP INDEX public.ix_user_tokens_uuid;
DROP INDEX public.ix_user_tokens_user_id;
DROP INDEX public.ix_user_tokens_refresh_token_jti;
DROP INDEX public.ix_user_tokens_access_token_jti;
DROP INDEX public.ix_sms_codes_uuid;
DROP INDEX public.ix_sms_codes_phone;
DROP INDEX public.ix_provinces_uuid;
DROP INDEX public.ix_provinces_code;
DROP INDEX public.ix_cities_uuid;
DROP INDEX public.ix_cities_province_code;
DROP INDEX public.ix_cities_code;
DROP INDEX public.ix_chat_messages_uuid;
DROP INDEX public.ix_chat_messages_user_id;
DROP INDEX public.ix_chat_messages_conversation_id;
DROP INDEX public.ix_car_recommendations_uuid;
DROP INDEX public.ix_car_recommendations_message_id;
DROP INDEX public.ix_car_recommendations_conversation_id;
DROP INDEX public.ix_car_recommendations_chat_message_uuid;
DROP INDEX public.ix_car_id_mappings_uuid;
DROP INDEX public.ix_car_id_mappings_real_car_id;
DROP INDEX public.ix_car_id_mappings_car_uuid;
DROP INDEX public.ix_car_comparisons_uuid;
DROP INDEX public.ix_car_comparisons_message_id;
DROP INDEX public.ix_car_comparisons_conversation_id;
DROP INDEX public.ix_car_comparisons_chat_message_uuid;
DROP INDEX public.ix_ai_conversations_uuid;
DROP INDEX public.ix_ai_conversations_user_id;
DROP INDEX public.idx_car_recommendations_car_uuid;
DROP INDEX public.idx_car_mappings_usage;
DROP INDEX public.idx_car_mappings_time_range;
DROP INDEX public.idx_car_mappings_active;
DROP INDEX public.idx_car_comparisons_conversation_cars;
DROP INDEX public.idx_car_comparisons_car_uuid_2;
DROP INDEX public.idx_car_comparisons_car_uuid_1;
ALTER TABLE ONLY public.users DROP CONSTRAINT users_pkey;
ALTER TABLE ONLY public.user_tokens DROP CONSTRAINT user_tokens_pkey;
ALTER TABLE ONLY public.sms_codes DROP CONSTRAINT sms_codes_pkey;
ALTER TABLE ONLY public.provinces DROP CONSTRAINT provinces_pkey;
ALTER TABLE ONLY public.cities DROP CONSTRAINT cities_pkey;
ALTER TABLE ONLY public.chat_messages DROP CONSTRAINT chat_messages_pkey;
ALTER TABLE ONLY public.car_recommendations DROP CONSTRAINT car_recommendations_pkey;
ALTER TABLE ONLY public.car_id_mappings DROP CONSTRAINT car_id_mappings_pkey;
ALTER TABLE ONLY public.car_comparisons DROP CONSTRAINT car_comparisons_pkey;
ALTER TABLE ONLY public.ai_conversations DROP CONSTRAINT ai_conversations_pkey;
ALTER TABLE public.users ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.user_tokens ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.sms_codes ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.provinces ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.cities ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.chat_messages ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.car_recommendations ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.car_id_mappings ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.car_comparisons ALTER COLUMN id DROP DEFAULT;
ALTER TABLE public.ai_conversations ALTER COLUMN id DROP DEFAULT;
DROP SEQUENCE public.users_id_seq;
DROP TABLE public.users;
DROP SEQUENCE public.user_tokens_id_seq;
DROP TABLE public.user_tokens;
DROP SEQUENCE public.sms_codes_id_seq;
DROP TABLE public.sms_codes;
DROP SEQUENCE public.provinces_id_seq;
DROP TABLE public.provinces;
DROP SEQUENCE public.cities_id_seq;
DROP TABLE public.cities;
DROP SEQUENCE public.chat_messages_id_seq;
DROP TABLE public.chat_messages;
DROP SEQUENCE public.car_recommendations_id_seq;
DROP TABLE public.car_recommendations;
DROP SEQUENCE public.car_id_mappings_id_seq;
DROP TABLE public.car_id_mappings;
DROP SEQUENCE public.car_comparisons_id_seq;
DROP TABLE public.car_comparisons;
DROP SEQUENCE public.ai_conversations_id_seq;
DROP TABLE public.ai_conversations;
SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: ai_conversations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.ai_conversations (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    user_id integer NOT NULL,
    title character varying(200) NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.ai_conversations OWNER TO postgres;

--
-- Name: COLUMN ai_conversations.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.id IS '主键ID';


--
-- Name: COLUMN ai_conversations.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.uuid IS '会话UUID';


--
-- Name: COLUMN ai_conversations.user_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.user_id IS '用户ID';


--
-- Name: COLUMN ai_conversations.title; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.title IS '会话标题';


--
-- Name: COLUMN ai_conversations.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.is_deleted IS '是否删除';


--
-- Name: COLUMN ai_conversations.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.created_at IS '创建时间';


--
-- Name: COLUMN ai_conversations.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.updated_at IS '更新时间';


--
-- Name: COLUMN ai_conversations.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.ai_conversations.deleted_at IS '删除时间';


--
-- Name: ai_conversations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.ai_conversations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.ai_conversations_id_seq OWNER TO postgres;

--
-- Name: ai_conversations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.ai_conversations_id_seq OWNED BY public.ai_conversations.id;


--
-- Name: car_comparisons; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.car_comparisons (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    conversation_id uuid NOT NULL,
    message_id character varying(100) NOT NULL,
    chat_message_uuid uuid,
    car_id_1 character varying(50) NOT NULL,
    car_id_2 character varying(50) NOT NULL,
    car_uuid_1 uuid NOT NULL,
    car_uuid_2 uuid NOT NULL,
    task_type character varying(50) NOT NULL,
    comparison_data json,
    slots_data json,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.car_comparisons OWNER TO postgres;

--
-- Name: COLUMN car_comparisons.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.id IS '主键ID';


--
-- Name: COLUMN car_comparisons.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.uuid IS '对比UUID';


--
-- Name: COLUMN car_comparisons.conversation_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.conversation_id IS '对话ID';


--
-- Name: COLUMN car_comparisons.message_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.message_id IS '关联的消息ID';


--
-- Name: COLUMN car_comparisons.chat_message_uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.chat_message_uuid IS '关联的聊天消息UUID';


--
-- Name: COLUMN car_comparisons.car_id_1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.car_id_1 IS '第一辆车的真实ID（不对外暴露）';


--
-- Name: COLUMN car_comparisons.car_id_2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.car_id_2 IS '第二辆车的真实ID（不对外暴露）';


--
-- Name: COLUMN car_comparisons.car_uuid_1; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.car_uuid_1 IS '第一辆车的对外UUID';


--
-- Name: COLUMN car_comparisons.car_uuid_2; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.car_uuid_2 IS '第二辆车的对外UUID';


--
-- Name: COLUMN car_comparisons.task_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.task_type IS '任务类型';


--
-- Name: COLUMN car_comparisons.comparison_data; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.comparison_data IS '完整对比数据';


--
-- Name: COLUMN car_comparisons.slots_data; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.slots_data IS '槽位数据';


--
-- Name: COLUMN car_comparisons.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.is_deleted IS '是否删除';


--
-- Name: COLUMN car_comparisons.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.created_at IS '创建时间';


--
-- Name: COLUMN car_comparisons.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.updated_at IS '更新时间';


--
-- Name: COLUMN car_comparisons.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_comparisons.deleted_at IS '删除时间';


--
-- Name: car_comparisons_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.car_comparisons_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.car_comparisons_id_seq OWNER TO postgres;

--
-- Name: car_comparisons_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.car_comparisons_id_seq OWNED BY public.car_comparisons.id;


--
-- Name: car_id_mappings; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.car_id_mappings (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    real_car_id character varying(50) NOT NULL,
    car_uuid uuid NOT NULL,
    first_seen_at timestamp with time zone DEFAULT now() NOT NULL,
    last_used_at timestamp with time zone DEFAULT now() NOT NULL,
    usage_count integer NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.car_id_mappings OWNER TO postgres;

--
-- Name: COLUMN car_id_mappings.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.id IS '主键ID';


--
-- Name: COLUMN car_id_mappings.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.uuid IS '映射记录UUID';


--
-- Name: COLUMN car_id_mappings.real_car_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.real_car_id IS '真实车辆ID';


--
-- Name: COLUMN car_id_mappings.car_uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.car_uuid IS '对外暴露的车辆UUID';


--
-- Name: COLUMN car_id_mappings.first_seen_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.first_seen_at IS '首次出现时间';


--
-- Name: COLUMN car_id_mappings.last_used_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.last_used_at IS '最后使用时间';


--
-- Name: COLUMN car_id_mappings.usage_count; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.usage_count IS '使用次数';


--
-- Name: COLUMN car_id_mappings.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.is_deleted IS '是否删除';


--
-- Name: COLUMN car_id_mappings.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.created_at IS '创建时间';


--
-- Name: COLUMN car_id_mappings.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.updated_at IS '更新时间';


--
-- Name: COLUMN car_id_mappings.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_id_mappings.deleted_at IS '删除时间';


--
-- Name: car_id_mappings_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.car_id_mappings_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.car_id_mappings_id_seq OWNER TO postgres;

--
-- Name: car_id_mappings_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.car_id_mappings_id_seq OWNED BY public.car_id_mappings.id;


--
-- Name: car_recommendations; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.car_recommendations (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    conversation_id uuid NOT NULL,
    message_id character varying(100) NOT NULL,
    chat_message_uuid uuid,
    car_id character varying(50) NOT NULL,
    task_type character varying(50) NOT NULL,
    recommendation_data json,
    slots_data json,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone,
    car_uuid uuid NOT NULL
);


ALTER TABLE public.car_recommendations OWNER TO postgres;

--
-- Name: COLUMN car_recommendations.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.id IS '主键ID';


--
-- Name: COLUMN car_recommendations.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.uuid IS '推荐UUID';


--
-- Name: COLUMN car_recommendations.conversation_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.conversation_id IS '对话ID';


--
-- Name: COLUMN car_recommendations.message_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.message_id IS '关联的消息ID';


--
-- Name: COLUMN car_recommendations.chat_message_uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.chat_message_uuid IS '关联的聊天消息UUID';


--
-- Name: COLUMN car_recommendations.car_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.car_id IS '真实车辆ID（不对外暴露）';


--
-- Name: COLUMN car_recommendations.task_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.task_type IS '任务类型';


--
-- Name: COLUMN car_recommendations.recommendation_data; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.recommendation_data IS '完整推荐数据';


--
-- Name: COLUMN car_recommendations.slots_data; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.slots_data IS '槽位数据';


--
-- Name: COLUMN car_recommendations.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.is_deleted IS '是否删除';


--
-- Name: COLUMN car_recommendations.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.created_at IS '创建时间';


--
-- Name: COLUMN car_recommendations.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.updated_at IS '更新时间';


--
-- Name: COLUMN car_recommendations.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.car_recommendations.deleted_at IS '删除时间';


--
-- Name: car_recommendations_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.car_recommendations_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.car_recommendations_id_seq OWNER TO postgres;

--
-- Name: car_recommendations_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.car_recommendations_id_seq OWNED BY public.car_recommendations.id;


--
-- Name: chat_messages; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.chat_messages (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    conversation_id uuid NOT NULL,
    user_id integer NOT NULL,
    message_type character varying(20) NOT NULL,
    content text NOT NULL,
    message_id character varying(100),
    is_complete boolean NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.chat_messages OWNER TO postgres;

--
-- Name: COLUMN chat_messages.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.id IS '主键ID';


--
-- Name: COLUMN chat_messages.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.uuid IS '消息UUID';


--
-- Name: COLUMN chat_messages.conversation_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.conversation_id IS '对话ID';


--
-- Name: COLUMN chat_messages.user_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.user_id IS '用户ID';


--
-- Name: COLUMN chat_messages.message_type; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.message_type IS '消息类型：user/assistant';


--
-- Name: COLUMN chat_messages.content; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.content IS '消息内容';


--
-- Name: COLUMN chat_messages.message_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.message_id IS 'AI返回的消息ID';


--
-- Name: COLUMN chat_messages.is_complete; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.is_complete IS '消息是否完整';


--
-- Name: COLUMN chat_messages.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.is_deleted IS '是否删除';


--
-- Name: COLUMN chat_messages.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.created_at IS '创建时间';


--
-- Name: COLUMN chat_messages.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.updated_at IS '更新时间';


--
-- Name: COLUMN chat_messages.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.chat_messages.deleted_at IS '删除时间';


--
-- Name: chat_messages_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.chat_messages_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.chat_messages_id_seq OWNER TO postgres;

--
-- Name: chat_messages_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.chat_messages_id_seq OWNED BY public.chat_messages.id;


--
-- Name: cities; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.cities (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    code character varying(10) NOT NULL,
    name character varying(50) NOT NULL,
    province_code character varying(10) NOT NULL,
    is_active boolean NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.cities OWNER TO postgres;

--
-- Name: COLUMN cities.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.id IS '主键ID';


--
-- Name: COLUMN cities.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.uuid IS 'UUID';


--
-- Name: COLUMN cities.code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.code IS '城市代码';


--
-- Name: COLUMN cities.name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.name IS '城市名称';


--
-- Name: COLUMN cities.province_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.province_code IS '所属省份代码';


--
-- Name: COLUMN cities.is_active; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.is_active IS '是否激活';


--
-- Name: COLUMN cities.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.is_deleted IS '是否删除';


--
-- Name: COLUMN cities.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.created_at IS '创建时间';


--
-- Name: COLUMN cities.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.updated_at IS '更新时间';


--
-- Name: COLUMN cities.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.cities.deleted_at IS '删除时间';


--
-- Name: cities_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.cities_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.cities_id_seq OWNER TO postgres;

--
-- Name: cities_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.cities_id_seq OWNED BY public.cities.id;


--
-- Name: provinces; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.provinces (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    code character varying(10) NOT NULL,
    name character varying(50) NOT NULL,
    is_active boolean NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.provinces OWNER TO postgres;

--
-- Name: COLUMN provinces.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.id IS '主键ID';


--
-- Name: COLUMN provinces.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.uuid IS 'UUID';


--
-- Name: COLUMN provinces.code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.code IS '省份代码';


--
-- Name: COLUMN provinces.name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.name IS '省份名称';


--
-- Name: COLUMN provinces.is_active; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.is_active IS '是否激活';


--
-- Name: COLUMN provinces.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.is_deleted IS '是否删除';


--
-- Name: COLUMN provinces.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.created_at IS '创建时间';


--
-- Name: COLUMN provinces.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.updated_at IS '更新时间';


--
-- Name: COLUMN provinces.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.provinces.deleted_at IS '删除时间';


--
-- Name: provinces_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.provinces_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.provinces_id_seq OWNER TO postgres;

--
-- Name: provinces_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.provinces_id_seq OWNED BY public.provinces.id;


--
-- Name: sms_codes; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.sms_codes (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    phone character varying(20) NOT NULL,
    code character varying(6) NOT NULL,
    purpose character varying(20) NOT NULL,
    is_used boolean NOT NULL,
    is_deleted boolean NOT NULL,
    expires_at timestamp with time zone NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.sms_codes OWNER TO postgres;

--
-- Name: COLUMN sms_codes.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.id IS '主键ID';


--
-- Name: COLUMN sms_codes.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.uuid IS 'UUID';


--
-- Name: COLUMN sms_codes.phone; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.phone IS '手机号';


--
-- Name: COLUMN sms_codes.code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.code IS '验证码';


--
-- Name: COLUMN sms_codes.purpose; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.purpose IS '用途';


--
-- Name: COLUMN sms_codes.is_used; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.is_used IS '是否已使用';


--
-- Name: COLUMN sms_codes.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.is_deleted IS '是否删除';


--
-- Name: COLUMN sms_codes.expires_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.expires_at IS '过期时间';


--
-- Name: COLUMN sms_codes.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.created_at IS '创建时间';


--
-- Name: COLUMN sms_codes.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.updated_at IS '更新时间';


--
-- Name: COLUMN sms_codes.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.sms_codes.deleted_at IS '删除时间';


--
-- Name: sms_codes_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.sms_codes_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.sms_codes_id_seq OWNER TO postgres;

--
-- Name: sms_codes_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.sms_codes_id_seq OWNED BY public.sms_codes.id;


--
-- Name: user_tokens; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.user_tokens (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    user_id integer NOT NULL,
    access_token_jti character varying(36) NOT NULL,
    refresh_token_jti character varying(36) NOT NULL,
    device_id character varying(100),
    device_name character varying(200),
    client_ip character varying(45),
    user_agent text,
    is_active boolean NOT NULL,
    is_deleted boolean NOT NULL,
    access_token_expires_at timestamp with time zone NOT NULL,
    refresh_token_expires_at timestamp with time zone NOT NULL,
    last_used_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone
);


ALTER TABLE public.user_tokens OWNER TO postgres;

--
-- Name: COLUMN user_tokens.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.id IS '主键ID';


--
-- Name: COLUMN user_tokens.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.uuid IS 'UUID';


--
-- Name: COLUMN user_tokens.user_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.user_id IS '用户ID';


--
-- Name: COLUMN user_tokens.access_token_jti; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.access_token_jti IS '访问令牌JTI';


--
-- Name: COLUMN user_tokens.refresh_token_jti; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.refresh_token_jti IS '刷新令牌JTI';


--
-- Name: COLUMN user_tokens.device_id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.device_id IS '设备ID';


--
-- Name: COLUMN user_tokens.device_name; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.device_name IS '设备名称';


--
-- Name: COLUMN user_tokens.client_ip; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.client_ip IS '客户端IP';


--
-- Name: COLUMN user_tokens.user_agent; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.user_agent IS '用户代理';


--
-- Name: COLUMN user_tokens.is_active; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.is_active IS '是否激活';


--
-- Name: COLUMN user_tokens.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.is_deleted IS '是否删除';


--
-- Name: COLUMN user_tokens.access_token_expires_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.access_token_expires_at IS '访问令牌过期时间';


--
-- Name: COLUMN user_tokens.refresh_token_expires_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.refresh_token_expires_at IS '刷新令牌过期时间';


--
-- Name: COLUMN user_tokens.last_used_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.last_used_at IS '最后使用时间';


--
-- Name: COLUMN user_tokens.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.created_at IS '创建时间';


--
-- Name: COLUMN user_tokens.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.updated_at IS '更新时间';


--
-- Name: COLUMN user_tokens.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.user_tokens.deleted_at IS '删除时间';


--
-- Name: user_tokens_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.user_tokens_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.user_tokens_id_seq OWNER TO postgres;

--
-- Name: user_tokens_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.user_tokens_id_seq OWNED BY public.user_tokens.id;


--
-- Name: users; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.users (
    id integer NOT NULL,
    uuid uuid NOT NULL,
    phone character varying(20) NOT NULL,
    username character varying(50),
    avatar_url character varying(500),
    province_code character varying(10),
    city_code character varying(10),
    is_active boolean NOT NULL,
    is_deleted boolean NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone,
    deleted_at timestamp with time zone,
    last_login timestamp with time zone
);


ALTER TABLE public.users OWNER TO postgres;

--
-- Name: COLUMN users.id; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.id IS '主键ID';


--
-- Name: COLUMN users.uuid; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.uuid IS 'UUID';


--
-- Name: COLUMN users.phone; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.phone IS '手机号';


--
-- Name: COLUMN users.username; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.username IS '用户名';


--
-- Name: COLUMN users.avatar_url; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.avatar_url IS '头像URL';


--
-- Name: COLUMN users.province_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.province_code IS '省份代码';


--
-- Name: COLUMN users.city_code; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.city_code IS '城市代码';


--
-- Name: COLUMN users.is_active; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.is_active IS '是否激活';


--
-- Name: COLUMN users.is_deleted; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.is_deleted IS '是否删除';


--
-- Name: COLUMN users.created_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.created_at IS '创建时间';


--
-- Name: COLUMN users.updated_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.updated_at IS '更新时间';


--
-- Name: COLUMN users.deleted_at; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.deleted_at IS '删除时间';


--
-- Name: COLUMN users.last_login; Type: COMMENT; Schema: public; Owner: postgres
--

COMMENT ON COLUMN public.users.last_login IS '最后登录时间';


--
-- Name: users_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.users_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.users_id_seq OWNER TO postgres;

--
-- Name: users_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.users_id_seq OWNED BY public.users.id;


--
-- Name: ai_conversations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_conversations ALTER COLUMN id SET DEFAULT nextval('public.ai_conversations_id_seq'::regclass);


--
-- Name: car_comparisons id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_comparisons ALTER COLUMN id SET DEFAULT nextval('public.car_comparisons_id_seq'::regclass);


--
-- Name: car_id_mappings id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_id_mappings ALTER COLUMN id SET DEFAULT nextval('public.car_id_mappings_id_seq'::regclass);


--
-- Name: car_recommendations id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_recommendations ALTER COLUMN id SET DEFAULT nextval('public.car_recommendations_id_seq'::regclass);


--
-- Name: chat_messages id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages ALTER COLUMN id SET DEFAULT nextval('public.chat_messages_id_seq'::regclass);


--
-- Name: cities id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cities ALTER COLUMN id SET DEFAULT nextval('public.cities_id_seq'::regclass);


--
-- Name: provinces id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.provinces ALTER COLUMN id SET DEFAULT nextval('public.provinces_id_seq'::regclass);


--
-- Name: sms_codes id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sms_codes ALTER COLUMN id SET DEFAULT nextval('public.sms_codes_id_seq'::regclass);


--
-- Name: user_tokens id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_tokens ALTER COLUMN id SET DEFAULT nextval('public.user_tokens_id_seq'::regclass);


--
-- Name: users id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users ALTER COLUMN id SET DEFAULT nextval('public.users_id_seq'::regclass);


--
-- Data for Name: ai_conversations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.ai_conversations (id, uuid, user_id, title, is_deleted, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: car_comparisons; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.car_comparisons (id, uuid, conversation_id, message_id, chat_message_uuid, car_id_1, car_id_2, car_uuid_1, car_uuid_2, task_type, comparison_data, slots_data, is_deleted, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: car_id_mappings; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.car_id_mappings (id, uuid, real_car_id, car_uuid, first_seen_at, last_used_at, usage_count, is_deleted, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: car_recommendations; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.car_recommendations (id, uuid, conversation_id, message_id, chat_message_uuid, car_id, task_type, recommendation_data, slots_data, is_deleted, created_at, updated_at, deleted_at, car_uuid) FROM stdin;
\.


--
-- Data for Name: chat_messages; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.chat_messages (id, uuid, conversation_id, user_id, message_type, content, message_id, is_complete, is_deleted, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: cities; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.cities (id, uuid, code, name, province_code, is_active, is_deleted, created_at, updated_at, deleted_at) FROM stdin;
371	30f0a57c-d7be-4306-95d9-1691410929cd	1301	石家庄市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
372	6fc27105-5575-4377-a3df-176ea5462641	1302	唐山市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
373	a0e97e04-906e-4343-8497-0fe485cb67df	1303	秦皇岛市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
374	72135f1b-b9e2-4ee7-b16c-c8765e7de395	1304	邯郸市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
375	b49b1f06-889f-4bfc-bd60-d17b9fef3be0	1305	邢台市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
376	02f2577c-5e7b-4cdc-98e5-99a55cd913d1	1306	保定市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
377	5a193600-19b0-4c2b-87bb-868f54392aed	1307	张家口市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
378	3ce16fbe-f1a0-476a-9969-27e560640257	1308	承德市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
379	309f53b4-f567-4df9-a460-cc24f64652d9	1309	沧州市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
380	15f4e03e-457c-4154-8a3b-8b394bfa4646	1310	廊坊市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
381	6437ac0b-5982-4c82-9045-7f5feef9a738	1311	衡水市	13	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
382	57db5113-24a6-40fc-afd1-b8833d9b5d9d	1401	太原市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
383	0255ffa2-75c5-4986-86b8-b13c04c1ee3c	1402	大同市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
384	0d8bc423-a766-46d1-8fa7-2475808e6f94	1403	阳泉市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
385	46f7e3b3-78d9-4383-9d20-87e99e5a745f	1404	长治市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
386	825f4862-601a-4065-b0d0-c80a1c8684d8	1405	晋城市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
387	af622d55-50fb-42f0-881f-7378dc77368f	1406	朔州市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
388	8718b487-b4c5-476f-9c8a-e78232ac59d8	1407	晋中市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
389	becafca0-9dfd-4647-ba4a-6951141001a8	1408	运城市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
390	d0474a28-98c4-47f6-8d59-044d44409a8d	1409	忻州市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
391	d9fdadb1-0fbb-40de-ae38-4794faaf4bd0	1410	临汾市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
392	8946c7b4-2f12-4028-8800-57cd63c39d9b	1411	吕梁市	14	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
393	f570d4de-1246-4cd9-b240-38e79680970f	1501	呼和浩特市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
394	8e8d0289-2eeb-42c9-98ca-d985149cba0c	1502	包头市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
395	68a08083-0b50-4c8f-a1a9-da204620afa2	1503	乌海市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
396	f38985ea-188f-4c97-9765-f667a5a451ce	1504	赤峰市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
397	1d67fb3a-fc61-40d8-a8bb-69f61af4e01b	1505	通辽市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
398	3ae4737c-7d6e-4e7a-a118-07cbdf0d97ca	1506	鄂尔多斯市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
399	12d30e8e-265d-4574-98f6-4cbf402df4a1	1507	呼伦贝尔市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
400	18deafa2-9086-485a-9ef2-6255d540ce7d	1508	巴彦淖尔市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
401	aa765c40-3fca-4ca5-8a7a-90a307529362	1509	乌兰察布市	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
402	1333f732-2c9e-4803-8cfe-cd9a0b036d55	1522	兴安盟	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
403	69344b89-469a-4a6b-abe1-8fa7f50687ea	1525	锡林郭勒盟	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
404	bfe4b67c-13af-4279-a05c-74b3c1be8e4a	1529	阿拉善盟	15	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
405	a47d0106-4e1e-435d-a782-a0460206eb9e	2101	沈阳市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
406	7afb4b4b-6ab0-4a73-9266-69f89937feae	2102	大连市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
407	412b6aaa-1940-4c15-bbee-febeff91828b	2103	鞍山市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
408	91b9d38a-d7bb-4185-b67c-0712a09804c2	2104	抚顺市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
409	80578391-14c7-45bc-831b-db3863d9f277	2105	本溪市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
410	499978af-6840-4e8f-b916-f7cacce0f9a1	2106	丹东市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
411	401d9714-9448-4a15-8dd3-2090df73489d	2107	锦州市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
412	f7854881-af33-402b-9235-8aedfa7d0e0a	2108	营口市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
413	9927e17d-55d4-4351-a50b-fdf9490d8c92	2109	阜新市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
414	6380c79e-232b-424c-8e78-ffe52b1613c3	2110	辽阳市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
415	3af75581-66c2-45dd-bb29-9e12165f3352	2111	盘锦市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
416	7d21fe4c-329a-485a-880b-094519c8db74	2112	铁岭市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
417	848ecc6b-5708-4eb0-8853-df6287f0518d	2113	朝阳市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
418	b18eee1b-ad90-46cd-9ea8-1591704449a0	2114	葫芦岛市	21	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
419	360b15ba-0b90-4b69-9d48-196aac9e239e	2201	长春市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
420	85a84915-b7bd-433a-88d8-a655af743c5f	2202	吉林市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
421	468e231d-0e07-4bf3-a55f-29992e572e91	2203	四平市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
422	6fe7d68d-9db6-4d20-aae8-211267357928	2204	辽源市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
423	3f8332cf-028d-4fa9-83c8-9d862acd1f4e	2205	通化市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
424	c4ad960f-ade6-4834-b465-9fb0946110ee	2206	白山市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
425	112cd2c4-bcdb-496d-b29e-601489a0c6e1	2207	松原市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
426	fcf18d95-884e-48b8-9a18-20413af60b90	2208	白城市	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
427	d7694794-9789-45b8-81dc-7f0eb5e497c6	2224	延边朝鲜族自治州	22	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
428	5a208b11-8aa6-41bb-816d-388f0d927dff	2301	哈尔滨市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
429	8cced04f-1a82-4e00-a481-4f66ad4d5574	2302	齐齐哈尔市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
430	ad9b4833-5957-46ca-9d9f-7c5103fa7353	2303	鸡西市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
431	f5510257-7290-404d-aab5-028a4e10943d	2304	鹤岗市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
432	a55b3d87-a882-480b-bd93-32e9d5c21a71	2305	双鸭山市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
433	9bb3acff-7796-448a-b492-1c9401b521bb	2306	大庆市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
434	e3898872-66a7-46c9-adbf-cf7f9cc4705f	2307	伊春市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
435	71d01928-531d-40be-975c-aa973624d506	2308	佳木斯市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
436	ce9b961b-cf6e-4cc8-886b-1d5940c27636	2309	七台河市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
437	55292cf2-c850-4542-8e18-38e204f0ce1c	2310	牡丹江市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
438	1f05c826-acfe-4293-92f6-dd9c2f32bffe	2311	黑河市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
439	7bf22e67-5918-407a-9ebb-ffa537d2de77	2312	绥化市	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
440	809708de-4919-4ccc-b9b2-a57418dd7d42	2327	大兴安岭地区	23	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
441	3b03b034-01a0-4b78-8e6b-4961a82b09cc	3201	南京市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
442	157b89b0-1711-4ca7-a6ba-75ba7bb930cb	3202	无锡市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
443	9b7f04a8-2bde-4638-b145-c4a9d7af4e24	3203	徐州市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
444	5cd5bea0-b514-43f3-a269-ba53eff3905c	3204	常州市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
445	a0fdae26-62e8-4910-a474-a4ba7299584e	3205	苏州市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
446	6a769c52-c36a-4811-82eb-1828ca494aea	3206	南通市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
447	d0557cd1-c8b4-42a7-84b4-846a1df9717e	3207	连云港市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
448	57d47a51-cb21-4173-9172-494a4dd0b4db	3208	淮安市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
449	1eaaaf7a-958a-4735-a0b9-60122f550dde	3209	盐城市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
450	a4e9f661-cbe4-4975-b199-e26f4dc2aa96	3210	扬州市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
451	9997fda1-fd4f-40a5-8450-785a0bc40117	3211	镇江市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
452	7c7236f9-f5b2-49fe-9cc4-c3fbd51e6da6	3212	泰州市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
453	d7463b3d-447f-42a4-8da8-64c64a8b73c1	3213	宿迁市	32	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
454	c8644af4-6b79-4dbf-9f4a-ec19d0896381	3301	杭州市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
455	3454a4e9-4498-4b71-9c52-665ab03902de	3302	宁波市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
456	77287e10-97ed-4d3f-8d62-a24ea4a516f5	3303	温州市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
457	2abcff33-6570-4f44-9d1c-38b67eacae91	3304	嘉兴市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
458	58075971-882c-4157-8e0c-e326c15ce3d0	3305	湖州市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
459	c5026816-cc2d-4186-97e0-aa51741152c9	3306	绍兴市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
460	d29ac51d-4683-4b52-ade4-563b7486e3f3	3307	金华市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
461	df15f71c-d657-46c7-b768-310fa5f1cfd7	3308	衢州市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
462	15d0a99c-2f55-4c0a-9737-4c5cc21e6a5a	3309	舟山市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
463	3eed4212-e972-414b-857e-1899e56b06a6	3310	台州市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
464	020223ae-9b00-4d85-afba-1457237cc8fc	3311	丽水市	33	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
465	26d6390f-74de-4588-9593-e06b0fa3190c	3401	合肥市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
466	05a1a0a3-1e82-4339-80d9-9f7f5f20b831	3402	芜湖市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
467	0587b0bc-6796-4933-862c-0fb6ba4df7a5	3403	蚌埠市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
468	27046822-7865-483a-add4-a65440342aaa	3404	淮南市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
469	fda1717f-5ae0-4113-8f8b-af7f2fb3d07d	3405	马鞍山市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
470	d01d5265-68c2-4918-808f-92e7894a6208	3406	淮北市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
471	2238add8-2454-4954-8e82-5aaa8bff97ba	3407	铜陵市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
472	5a4f7a2d-ba76-4006-8dc0-44bcaecf3cc0	3408	安庆市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
473	f31a6a23-62ba-4b10-9291-4f733e951c6e	3410	黄山市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
474	163e35dd-dbc2-4032-ab0b-414cb6d71fe9	3411	滁州市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
475	50f994ce-d6df-4605-98a6-c1cfdffd5215	3412	阜阳市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
476	8cd13e6b-2eff-4a47-8f61-5799dbd261f4	3413	宿州市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
477	0f282dc0-d28c-43e8-83ab-18d50c6a923f	3415	六安市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
478	552d2219-b159-45ef-a6cd-683fadc451bb	3416	亳州市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
479	566944e6-ffc6-475e-9c80-241449da89eb	3417	池州市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
480	55ffab4e-8bc4-46de-aa1a-a9b55ea4ccac	3418	宣城市	34	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
481	ed936f54-80bd-49ee-80fa-62199edfd661	3501	福州市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
482	72898583-c1bf-49aa-8ba0-aa7a2b7edf24	3502	厦门市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
483	6941e170-09d4-4b6c-8b3f-96d02c767b64	3503	莆田市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
484	e1afa63a-7bcc-4ca2-b482-e45d695d068f	3504	三明市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
485	0608fadc-5156-4a14-96b8-e220837a7223	3505	泉州市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
486	898aa525-1d90-4e78-9f2a-d0c0221f1a4b	3506	漳州市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
487	aa20e8d6-280d-4651-a241-f3ca58a91de6	3507	南平市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
488	90266645-1461-445b-98c9-0834ce7b073d	3508	龙岩市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
489	6a73e609-e7b2-428f-9682-a6e1c3b130cd	3509	宁德市	35	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
490	610b55c1-794f-429d-ae9a-6a337a4edc38	3601	南昌市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
491	89a6e657-139c-4bb4-8469-476e1f9e6029	3602	景德镇市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
492	a0f83a14-747f-4b27-9438-301739e3fdea	3603	萍乡市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
493	75fba52c-4aaa-4120-bb09-b9e74f05684e	3604	九江市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
494	d9ec8c4e-1899-4b56-bc9d-d68c2a9fb7fd	3605	新余市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
495	ac97cd62-5eb6-435e-8bc0-42a5171051d8	3606	鹰潭市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
496	dec57727-3d35-48c9-a6ac-24f0ff1932a9	3607	赣州市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
497	5baa9c5c-af7f-49e9-8518-7d7025cdc569	3608	吉安市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
498	e36f8f49-4bd1-4fd2-89de-ec804c6e5841	3609	宜春市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
499	577c72e8-0fac-4cbb-b12a-2eba0fbe1e5f	3610	抚州市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
500	f93639ac-1823-45c9-a8d0-128ea26b1c17	3611	上饶市	36	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
501	b68792fd-ab52-4d7e-a3cd-2eadc2ba49ff	3701	济南市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
502	a966bfb6-a011-467d-85cd-e65954ca7d58	3702	青岛市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
503	e5560211-ded9-4cec-8837-7170221e6f5e	3703	淄博市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
504	36cd11e0-4817-44d3-881a-8d709202092f	3704	枣庄市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
505	57cf2393-f303-4df2-ad9f-ae2f37b9bc69	3705	东营市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
506	b1b5b3b1-974e-43b1-9aee-b0027159d39b	3706	烟台市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
507	59c015a8-1b3d-48ee-9908-065ad308b6a2	3707	潍坊市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
508	b4cae18a-1e05-43bb-97c6-4b60d9d16e1c	3708	济宁市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
509	920795bc-89bd-4846-9117-ab40211ec903	3709	泰安市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
510	61d969d1-f6a6-4b23-b4c9-51946c2de1bb	3710	威海市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
511	2aa0209b-1fe4-4baf-8bc8-4d497895bc4e	3711	日照市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
512	929a2580-f49f-4c54-9761-f241b6a9c826	3713	临沂市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
513	1cffaec1-ccf8-4ecc-95ee-394d246c37a3	3714	德州市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
514	b95d6cbb-972c-4cff-a12c-b1a608847383	3715	聊城市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
515	bda0e8bc-313a-4d0d-8ca2-3701b167dd05	3716	滨州市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
516	5767f763-53f6-40de-996f-81b6b94dc919	3717	菏泽市	37	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
517	94ebb250-c0d5-4b6b-aa86-5e74fe3af5b3	4101	郑州市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
518	cff4d4b9-e85d-4746-a3ce-5a8f8a9041c1	4102	开封市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
519	601cd8c1-524d-48e6-af74-8890bc5c285d	4103	洛阳市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
520	5102dd89-d0b5-4faf-8f35-0baca16a26a0	4104	平顶山市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
521	f0c346a6-d993-41ea-b069-a0869ebef9cd	4105	安阳市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
522	0a9bac6c-e028-4bfd-bbe5-e21d6c9c0cc3	4106	鹤壁市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
523	bdb83c20-07bf-4784-8312-167f88b648a0	4107	新乡市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
524	305736c1-00d6-4fcf-9ddf-9ac847f4fa59	4108	焦作市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
525	f3c2cd90-9055-474d-9bae-98f250557099	4109	濮阳市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
526	0680424c-90aa-41a2-9cfb-e6904abbb0eb	4110	许昌市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
527	7d27ee03-5194-4814-bf50-dfe93b733cf6	4111	漯河市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
528	5ea676cb-913f-44b2-a014-572a91c4ec45	4112	三门峡市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
529	05293f0d-b60b-4e20-b22c-530aeb8cf0b0	4113	南阳市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
530	e6833663-d3c1-4ad6-bcab-e8d2e8a4b935	4114	商丘市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
531	42f1562c-74fd-4c29-ab06-1b41064d26c8	4115	信阳市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
532	609b9473-397c-4069-baf6-07bf6647e0df	4116	周口市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
533	741e6ca3-2add-47c0-be1a-3946811b6dcc	4117	驻马店市	41	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
534	1fecfd62-37b9-48aa-b874-999591ee1003	4201	武汉市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
535	9f75f3e3-f3f7-4c6c-8bda-3ac1d2218bce	4202	黄石市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
536	5a52e8cf-173c-4fab-9420-d6fa68103d8e	4203	十堰市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
537	6a8966a3-4481-4c49-aa48-40afd08603cf	4205	宜昌市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
538	f6c3946f-1418-489c-bcf1-456a42563df3	4206	襄阳市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
539	3c5a52d4-fb81-48a5-b022-64cdab6dcca7	4207	鄂州市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
540	d0fd6250-6312-4bdd-b435-0c569f78d240	4208	荆门市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
541	551bc9ed-3ed2-44cc-b65e-71e00bd4459b	4209	孝感市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
542	efcffac3-5c0d-400b-a4e3-bccdbe887d01	4210	荆州市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
543	151135a1-a3d8-4d56-acc1-acbfdb5b4467	4211	黄冈市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
544	e4769b27-9810-491f-99b4-8990d63c117f	4212	咸宁市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
545	085aec81-e6b0-4d5a-b5a0-e2ab4d0837cb	4213	随州市	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
546	9a83e620-1d02-4ee4-8235-b95782fc9bd9	4228	恩施土家族苗族自治州	42	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
547	0a1ef368-c408-4fae-b5bf-0f0fc8a34110	4301	长沙市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
548	729e2e27-d5f6-46a6-b49c-fa3c8e0ae31c	4302	株洲市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
549	3e0b4479-c3a7-4017-9822-bad28b8b5f15	4303	湘潭市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
550	e1121467-a64e-4d70-b27f-4a65d9740842	4304	衡阳市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
551	acb42b57-4ed5-473a-b4bd-709c5e142a80	4305	邵阳市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
552	f08d63f2-c494-40f3-af14-2c775fd332f7	4306	岳阳市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
553	4a9026d4-4368-4f94-b6ae-7121375c9f22	4307	常德市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
554	9c3342f1-cbb1-4611-9319-dd4f4aacc682	4308	张家界市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
555	796966ca-de89-4dbe-8bf4-d5fb610b3dba	4309	益阳市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
556	fb7d3b36-137c-4c65-9058-5fc18796c6f2	4310	郴州市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
557	c6785003-9d55-4228-8e04-3c9ed1029524	4311	永州市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
558	e7a8e34c-a88b-4863-be51-3d3cc6a1bc9a	4312	怀化市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
559	f1cd2f9f-2088-4978-9271-d3ffed49a673	4313	娄底市	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
560	02737815-1b6b-4368-8184-7e895109d16a	4331	湘西土家族苗族自治州	43	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
561	eb1106f2-f439-42eb-89bb-42f4c26fefef	4401	广州市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
562	7f629daa-a5d7-4fdd-9262-7de5d00b0fd8	4402	韶关市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
563	57d7e554-1fcf-4aa0-a6d5-5cb39613539a	4403	深圳市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
564	bdab2eaa-b73f-4b48-b331-65244b8b33f3	4404	珠海市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
565	2b99612a-8ced-479d-a651-21bb3b3ce7c6	4405	汕头市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
566	ba2cd1e1-2cd1-48e5-860a-c3875e458a2d	4406	佛山市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
567	5e696b3b-2028-4021-b584-17c748e9706a	4407	江门市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
568	ca1ceabc-7843-4534-8978-08b0105c77b7	4408	湛江市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
569	c898906c-f0ca-41a6-81cb-9dff119c27d9	4409	茂名市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
570	636f2c8b-18e4-4dfb-9851-204b434eadb0	4412	肇庆市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
571	e69de1d6-95da-45ba-9a23-9ec7b895578d	4413	惠州市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
572	c579a480-e611-476c-9207-f5383ea0f821	4414	梅州市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
573	62c36957-2ce5-4435-9041-ed294a622e32	4415	汕尾市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
574	438c1924-ae27-456a-b95a-77c6d3502235	4416	河源市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
575	b79e2478-c158-46dc-8331-3234764c9b72	4417	阳江市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
576	0aec4f4b-a736-471d-970a-9d7dce8a1fcc	4418	清远市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
577	5ab90bd1-80e4-4ad2-a026-4320460e3cb6	4419	东莞市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
578	c311eabc-2321-45c9-90d7-80f5ef7e1b31	4420	中山市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
579	bb35791f-8ef5-490c-946b-30cd37a571ee	4451	潮州市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
580	e03269ff-9f2c-4851-bd44-ca1feaee58db	4452	揭阳市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
581	df0444a1-fb50-4184-87cc-05e38558ea90	4453	云浮市	44	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
582	25a49b3f-8ee0-450c-bfa7-98a4ab27a050	4501	南宁市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
583	afca43b9-e96f-44df-84f3-d15809783cfc	4502	柳州市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
584	c7dec18c-578d-4052-b048-108f5ca41c61	4503	桂林市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
585	1ba00b82-5422-40d4-aa53-300a0910b6bd	4504	梧州市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
586	229c7d00-63e9-4057-9ac2-5d888a43d0fe	4505	北海市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
587	47a2d6be-727d-42d1-b906-4a04f16c83c7	4506	防城港市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
588	306cf4c8-7d76-41eb-8a5d-d594ce2bbb5a	4507	钦州市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
589	f3d164c0-d23f-41b7-b543-65dade2e5aa2	4508	贵港市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
590	317033c3-a5b8-46fe-bd06-abae77822104	4509	玉林市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
591	5e8ebdb1-7668-41f3-b123-f7372ed68c15	4510	百色市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
592	18e3cda4-4004-4de6-a855-9317ff8562ee	4511	贺州市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
593	ee1b74a7-5e86-4026-b47e-db43e984b289	4512	河池市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
594	e063a93b-fcd1-4721-931d-d869833e07b0	4513	来宾市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
595	e823cba9-9edf-42bf-8ca9-b22cf7aef55b	4514	崇左市	45	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
596	db0e88dc-065a-4763-ac2d-258a374209a3	4601	海口市	46	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
597	47e91c7f-2acf-4d3e-aea4-a8e37b877191	4602	三亚市	46	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
598	dc7ae7e7-0d4d-480f-ac7f-fd080dac0fd3	4603	三沙市	46	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
599	add4c6b7-6c85-4030-bc25-874698bc2d2c	4604	儋州市	46	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
600	a8e26672-7cce-4169-80af-61779aa533c2	5101	成都市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
601	7f530d46-18be-4153-9e10-5d95d61873e9	5103	自贡市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
602	d1e2b92e-81d1-42f1-8c0f-1cfdf3bb1e8e	5104	攀枝花市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
603	47b2e079-e078-4efe-85c3-50e6ba80ac76	5105	泸州市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
604	9e532075-6cad-42d8-b798-f7c4f471bde6	5106	德阳市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
605	fff6d5a4-e08a-427b-8775-1dbf1b976a36	5107	绵阳市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
606	fb75f658-be83-4ae5-8884-82ca405af495	5108	广元市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
607	caf03089-a025-449d-add4-a1184191c320	5109	遂宁市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
608	77917722-7786-4312-a007-d336c046a89b	5110	内江市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
609	b014e553-c1b9-4c79-979f-7f597bbe2eb0	5111	乐山市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
610	0faedcf6-d7c0-4a18-addf-ef5f3fb5b263	5113	南充市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
611	224cd93e-d07e-4c94-8cad-d6306788edad	5114	眉山市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
612	14be365e-c15b-4f4d-944b-d83eda4bd838	5115	宜宾市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
613	d314a648-cd0a-44d6-bc25-b350622a1194	5116	广安市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
614	103eccbc-7051-4317-a66a-b719fa0c49a8	5117	达州市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
615	7dbd4fe2-2ec1-4285-858e-40ea53d8dfc3	5118	雅安市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
616	3d9b455d-8e0f-4596-b8ba-a9f716c495b2	5119	巴中市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
617	28c93106-fbed-4bf5-ae4d-5af8e3b0b3df	5120	资阳市	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
618	fde72673-0723-4f6f-aea9-89886c3f621c	5132	阿坝藏族羌族自治州	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
619	b81c92ce-123d-4b82-91a9-5aa2a455f34f	5133	甘孜藏族自治州	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
620	ef53458e-69d0-4648-abca-440aeabe445f	5134	凉山彝族自治州	51	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
621	75726322-3c7a-40e7-80c1-b15c2722fca5	5201	贵阳市	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
622	b777726b-aff2-4462-80d2-d061e85efa19	5202	六盘水市	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
623	da96d2b3-1cf6-4dee-8e6d-79e09e887b79	5203	遵义市	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
624	4fcfe8ad-a901-4612-93af-04c1649f52c6	5204	安顺市	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
625	f5905aaa-25bb-4472-881a-a1bb650e3676	5205	毕节市	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
626	402ca71a-bb8f-49c5-b262-b47273350dd4	5206	铜仁市	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
627	e2f361f9-e8dc-4bff-9785-03bec14bdb9b	5223	黔西南布依族苗族自治州	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
628	9b9601a0-2119-4690-bc2c-e0ff2e2c4b49	5226	黔东南苗族侗族自治州	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
629	b8e060bb-8770-4ea5-b968-268ec4490bff	5227	黔南布依族苗族自治州	52	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
630	fbbc4a06-b730-4cce-82e7-3d8a1fa90123	5301	昆明市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
631	68aa919a-9edc-49b3-9fa7-930960a254de	5303	曲靖市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
632	697648c3-fbb7-4539-91fb-7e7aab505d99	5304	玉溪市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
633	114e6d30-02e5-417c-8872-84063c82ef2a	5305	保山市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
634	2d9e5635-e7df-418d-9a07-4b6a5dd863e6	5306	昭通市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
635	d7348677-4f20-438c-be6a-859a2c4ac85f	5307	丽江市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
636	188623ad-dc41-4cf4-9983-b5f3d2a96adb	5308	普洱市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
637	d9941c82-ed9f-440f-823d-a4433a48d49c	5309	临沧市	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
638	f7c565f2-d506-49ba-b617-36cc77089d5f	5323	楚雄彝族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
639	0f26e859-e66d-4816-8bb5-787af8ba2a22	5325	红河哈尼族彝族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
640	e6e2c455-9187-45b8-8a93-6568e43c199e	5326	文山壮族苗族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
641	88f54dae-574a-4206-8f93-aa27bfa80b98	5328	西双版纳傣族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
642	01aec9d5-e697-44ae-8811-409c3066fe9d	5329	大理白族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
643	c43a15bd-2e72-4ce9-8cb4-73de9339582b	5331	德宏傣族景颇族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
644	15e28731-4fe7-47cd-ad0f-d95466a244d0	5333	怒江傈僳族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
645	40177a37-4456-478d-bf1e-aa7d569b7c66	5334	迪庆藏族自治州	53	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
646	4b43bcdc-8df8-44c9-a770-fd23a8a92159	5401	拉萨市	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
647	fa1c5655-9868-4025-89a7-d4cfcd5e0bfb	5402	日喀则市	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
648	e3d0c747-8f57-4e5b-b800-a61eb857d394	5403	昌都市	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
649	ea8fa12f-9e01-4919-8f72-437eb19f1187	5404	林芝市	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
650	aea1539a-1e78-49f1-83dc-64cd78e69a93	5405	山南市	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
651	ac4b282b-31b7-456b-be12-a501aa4a7a7a	5406	那曲市	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
652	7902f482-edf3-42e7-8e35-d87d1254fb03	5425	阿里地区	54	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
653	6f9a9129-14b5-4748-8c26-36911f119515	6101	西安市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
654	8abe24e7-e589-4806-bc95-fef3acea2b26	6102	铜川市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
655	4e113c8b-13b7-4e7b-ba6b-b0db2c3e17e8	6103	宝鸡市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
656	2bd29c2a-540e-45f5-a63c-526c25924333	6104	咸阳市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
657	eadda32d-3071-4795-8d6c-c5aa04e48104	6105	渭南市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
658	3488e051-e169-44be-bf93-c588eb9dd755	6106	延安市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
659	8fc1f721-c7f0-4421-9a19-2e3584756228	6107	汉中市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
660	d2261298-db4f-4f46-b268-3402206ce29d	6108	榆林市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
661	c1d04f35-b546-4cb3-9e57-3b94370f2ead	6109	安康市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
662	0ad08a91-39b6-4f56-8bd5-242d9b8dd82f	6110	商洛市	61	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
663	705e5560-f913-4407-adbc-dc9966d12af2	6201	兰州市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
664	28c18c0a-5060-47fc-8aac-09ca20dfd85f	6202	嘉峪关市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
665	e2295689-5e99-426f-9472-3034eda01e4d	6203	金昌市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
666	3c5084dd-7251-4f48-bf6d-5ffa3f02d9af	6204	白银市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
667	395d47c6-7dd8-4b29-9591-da23b9068933	6205	天水市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
668	453633ea-8cca-46ad-92a3-181a2eeeaeb0	6206	武威市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
669	961866ac-4254-4da4-9717-ab8d83e1d674	6207	张掖市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
670	f5323191-f5bf-4379-9e3a-8785d2c23bbf	6208	平凉市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
671	22b93c6b-7383-42d0-bbc6-0a531b0e983f	6209	酒泉市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
672	8278afd3-99de-4a87-bd62-2075835bee74	6210	庆阳市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
673	47cde9ec-3d0d-440b-be75-592ac2caaec8	6211	定西市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
674	1957788e-26b5-4a59-9d97-e09bddf5f142	6212	陇南市	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
675	7968c9cf-f4ee-4b9d-af88-e6e7d9e3297f	6229	临夏回族自治州	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
676	7e8b98d9-d911-499c-b9df-866dd76eb51d	6230	甘南藏族自治州	62	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
677	fba11aed-fbb4-48e2-9a49-bc363116a206	6301	西宁市	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
678	e5bfdf85-a4fc-46db-9b0d-ab1b6c0a1c23	6302	海东市	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
679	bd3f8034-912b-4ec2-bdf1-d869eb84c8a9	6322	海北藏族自治州	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
680	6eafae24-9806-43f9-8579-e1745a0535d5	6323	黄南藏族自治州	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
681	cab214ed-ac18-4705-b860-a3e602568d7f	6325	海南藏族自治州	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
682	7ce79bef-47ce-42ee-8f61-8f9a36783db8	6326	果洛藏族自治州	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
683	e490d813-b5f3-400c-8a12-3e280e407713	6327	玉树藏族自治州	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
684	6405ca48-c97b-4222-84e4-d47647aff0e2	6328	海西蒙古族藏族自治州	63	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
685	57ab44af-74f5-4121-94d2-2209357a2560	6401	银川市	64	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
686	debe8fe4-c49c-4070-889d-da27d0820cec	6402	石嘴山市	64	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
687	7393c85a-5cb9-4193-aa49-5816ad83549c	6403	吴忠市	64	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
688	e51c0cc5-673e-43ed-ae3e-ba0f9d0fb47b	6404	固原市	64	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
689	6af34718-b523-45fc-bc8c-cd65303a4f32	6405	中卫市	64	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
690	55ef40b7-e5b7-4d56-84d5-fd4b1e6ff99c	6501	乌鲁木齐市	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
691	3904434c-a0d4-4aaa-ae66-c144dfd33500	6502	克拉玛依市	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
692	cd1ddcc6-f990-4366-ae99-9a4338dea011	6504	吐鲁番市	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
693	24de6c7f-477a-468d-b3c8-8e8b48a33129	6505	哈密市	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
694	f2aab92c-f0fd-458e-bedd-d737d928b1ab	6523	昌吉回族自治州	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
695	ac344ee2-261c-4c44-bd4e-143bb4502c28	6527	博尔塔拉蒙古自治州	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
696	c20ff404-3920-428c-b66e-1d9fb0bb758d	6528	巴音郭楞蒙古自治州	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
697	3fd0936b-9cf5-4665-9faa-c36cfd99ebae	6529	阿克苏地区	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
698	badb56c1-7b0e-4dd3-987e-996c8e539263	6530	克孜勒苏柯尔克孜自治州	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
699	82635aa8-4db1-447e-8d62-bc2f7f39e28d	6531	喀什地区	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
700	11eaa0be-897a-4e41-8a66-f5b8d03720ee	6532	和田地区	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
701	9cdd93cc-4d50-450a-a230-0b20e23443bb	6540	伊犁哈萨克自治州	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
702	533f48b8-fcf7-4133-8d11-25968b447001	6542	塔城地区	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
703	e81c94ca-c085-41da-be44-76386e2d37a0	6543	阿勒泰地区	65	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
704	0d6c80fa-96aa-40fd-85cf-4286e78f19bd	1101	北京市	11	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
705	42dd905a-9d55-46ec-bbc0-8f37153cf027	1201	天津市	12	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
706	170bbcbe-3b84-41bf-b9d1-c3a61b4114b5	3101	上海市	31	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
707	3b1edec0-36a6-4aa5-b61b-75a5fa591a50	5001	重庆市	50	t	f	2025-07-25 09:31:06.918959+02	2025-07-25 09:31:06.918959+02	\N
\.


--
-- Data for Name: provinces; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.provinces (id, uuid, code, name, is_active, is_deleted, created_at, updated_at, deleted_at) FROM stdin;
35	56960f40-d775-4140-aaee-8b0e670cb685	11	北京市	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
36	851ba12a-311b-4078-bed4-c822c21269e5	12	天津市	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
37	cab26f75-6ef3-4a55-909d-cf4b80c9db10	13	河北省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
38	b4051625-49df-4ec0-af43-383debd44ab3	14	山西省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
39	18f37d2d-f42f-4080-acd1-85a9b8bf58d9	15	内蒙古自治区	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
40	05ad3438-1f97-488a-8de4-3174882d69eb	21	辽宁省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
41	670be0c7-830d-4920-bc62-a78df494d409	22	吉林省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
42	2e3c2aba-2d32-44ba-a8db-5f9738781e63	23	黑龙江省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
43	687666cf-1f92-401b-9838-03c6d072cec2	31	上海市	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
44	c54646d4-3954-4d15-8655-257755bfafad	32	江苏省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
45	0fcc1aff-3410-4879-92f7-fec147e4f26a	33	浙江省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
46	44fc59c3-dbd9-4c7b-9708-d1a4583703f4	34	安徽省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
47	3e467656-1450-4c49-9535-4a0fa76d600d	35	福建省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
48	241de7a7-4e17-4f70-8135-f256d45c0b60	36	江西省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
49	951fc686-1a5a-4d13-ae99-48d7aec799a1	37	山东省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
50	ef94b7f1-4dbc-4deb-86e2-379e6ba1a8eb	41	河南省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
51	91123696-06d2-4c43-bed1-31f7f978ef57	42	湖北省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
52	9fa15a79-a1ac-4224-a4b9-05b7155e66b6	43	湖南省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
53	9390041c-ebdf-434f-8a12-dcd5f62f49b3	44	广东省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
54	f3477d24-76bf-4598-a125-1e3593f773a2	45	广西壮族自治区	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
55	eb58b5f1-94ac-474c-be6b-ed38519fa696	46	海南省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
56	da1db272-bc72-447d-bd1a-8a34b96e28f1	50	重庆市	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
57	e53d2e23-50ac-4719-9116-cf06039b5737	51	四川省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
58	2243b101-ac20-4dcd-a8e8-a3510135ffd5	52	贵州省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
59	fc6020db-23ae-446c-8dbe-509f50c79514	53	云南省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
60	b692bfb5-e9e5-4373-b76b-92537c627a85	54	西藏自治区	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
61	c0fe05ee-e539-4b13-8865-6591409db03c	61	陕西省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
62	83eef896-494d-4cb6-bae7-d56a9399fc2a	62	甘肃省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
63	68252ba0-56bf-4897-ac18-4d1ce6e039c8	63	青海省	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
64	8b753028-f6de-4b33-8c00-4ee432085e0b	64	宁夏回族自治区	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
65	c7ace0a3-cb07-4e8a-9afa-41457df0ad6a	65	新疆维吾尔自治区	t	f	2025-07-25 09:31:05.90343+02	2025-07-25 09:31:05.90343+02	\N
\.


--
-- Data for Name: sms_codes; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.sms_codes (id, uuid, phone, code, purpose, is_used, is_deleted, expires_at, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: user_tokens; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.user_tokens (id, uuid, user_id, access_token_jti, refresh_token_jti, device_id, device_name, client_ip, user_agent, is_active, is_deleted, access_token_expires_at, refresh_token_expires_at, last_used_at, created_at, updated_at, deleted_at) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.users (id, uuid, phone, username, avatar_url, province_code, city_code, is_active, is_deleted, created_at, updated_at, deleted_at, last_login) FROM stdin;
\.


--
-- Name: ai_conversations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.ai_conversations_id_seq', 51, true);


--
-- Name: car_comparisons_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.car_comparisons_id_seq', 10, true);


--
-- Name: car_id_mappings_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.car_id_mappings_id_seq', 31, true);


--
-- Name: car_recommendations_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.car_recommendations_id_seq', 19, true);


--
-- Name: chat_messages_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.chat_messages_id_seq', 169, true);


--
-- Name: cities_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.cities_id_seq', 707, true);


--
-- Name: provinces_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.provinces_id_seq', 65, true);


--
-- Name: sms_codes_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.sms_codes_id_seq', 38, true);


--
-- Name: user_tokens_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.user_tokens_id_seq', 36, true);


--
-- Name: users_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.users_id_seq', 7, true);


--
-- Name: ai_conversations ai_conversations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_conversations
    ADD CONSTRAINT ai_conversations_pkey PRIMARY KEY (id);


--
-- Name: car_comparisons car_comparisons_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_comparisons
    ADD CONSTRAINT car_comparisons_pkey PRIMARY KEY (id);


--
-- Name: car_id_mappings car_id_mappings_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_id_mappings
    ADD CONSTRAINT car_id_mappings_pkey PRIMARY KEY (id);


--
-- Name: car_recommendations car_recommendations_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_recommendations
    ADD CONSTRAINT car_recommendations_pkey PRIMARY KEY (id);


--
-- Name: chat_messages chat_messages_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_pkey PRIMARY KEY (id);


--
-- Name: cities cities_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.cities
    ADD CONSTRAINT cities_pkey PRIMARY KEY (id);


--
-- Name: provinces provinces_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.provinces
    ADD CONSTRAINT provinces_pkey PRIMARY KEY (id);


--
-- Name: sms_codes sms_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.sms_codes
    ADD CONSTRAINT sms_codes_pkey PRIMARY KEY (id);


--
-- Name: user_tokens user_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_tokens
    ADD CONSTRAINT user_tokens_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: idx_car_comparisons_car_uuid_1; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_comparisons_car_uuid_1 ON public.car_comparisons USING btree (car_uuid_1);


--
-- Name: idx_car_comparisons_car_uuid_2; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_comparisons_car_uuid_2 ON public.car_comparisons USING btree (car_uuid_2);


--
-- Name: idx_car_comparisons_conversation_cars; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_comparisons_conversation_cars ON public.car_comparisons USING btree (conversation_id, car_uuid_1, car_uuid_2);


--
-- Name: idx_car_mappings_active; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_mappings_active ON public.car_id_mappings USING btree (is_deleted, real_car_id) WHERE (is_deleted = false);


--
-- Name: idx_car_mappings_time_range; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_mappings_time_range ON public.car_id_mappings USING btree (created_at, last_used_at);


--
-- Name: idx_car_mappings_usage; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_mappings_usage ON public.car_id_mappings USING btree (usage_count DESC, last_used_at DESC);


--
-- Name: idx_car_recommendations_car_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_car_recommendations_car_uuid ON public.car_recommendations USING btree (car_uuid);


--
-- Name: ix_ai_conversations_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_ai_conversations_user_id ON public.ai_conversations USING btree (user_id);


--
-- Name: ix_ai_conversations_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_ai_conversations_uuid ON public.ai_conversations USING btree (uuid);


--
-- Name: ix_car_comparisons_chat_message_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_car_comparisons_chat_message_uuid ON public.car_comparisons USING btree (chat_message_uuid);


--
-- Name: ix_car_comparisons_conversation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_car_comparisons_conversation_id ON public.car_comparisons USING btree (conversation_id);


--
-- Name: ix_car_comparisons_message_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_car_comparisons_message_id ON public.car_comparisons USING btree (message_id);


--
-- Name: ix_car_comparisons_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_car_comparisons_uuid ON public.car_comparisons USING btree (uuid);


--
-- Name: ix_car_id_mappings_car_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_car_id_mappings_car_uuid ON public.car_id_mappings USING btree (car_uuid);


--
-- Name: ix_car_id_mappings_real_car_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_car_id_mappings_real_car_id ON public.car_id_mappings USING btree (real_car_id);


--
-- Name: ix_car_id_mappings_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_car_id_mappings_uuid ON public.car_id_mappings USING btree (uuid);


--
-- Name: ix_car_recommendations_chat_message_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_car_recommendations_chat_message_uuid ON public.car_recommendations USING btree (chat_message_uuid);


--
-- Name: ix_car_recommendations_conversation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_car_recommendations_conversation_id ON public.car_recommendations USING btree (conversation_id);


--
-- Name: ix_car_recommendations_message_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_car_recommendations_message_id ON public.car_recommendations USING btree (message_id);


--
-- Name: ix_car_recommendations_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_car_recommendations_uuid ON public.car_recommendations USING btree (uuid);


--
-- Name: ix_chat_messages_conversation_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_chat_messages_conversation_id ON public.chat_messages USING btree (conversation_id);


--
-- Name: ix_chat_messages_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_chat_messages_user_id ON public.chat_messages USING btree (user_id);


--
-- Name: ix_chat_messages_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_chat_messages_uuid ON public.chat_messages USING btree (uuid);


--
-- Name: ix_cities_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_cities_code ON public.cities USING btree (code);


--
-- Name: ix_cities_province_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_cities_province_code ON public.cities USING btree (province_code);


--
-- Name: ix_cities_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_cities_uuid ON public.cities USING btree (uuid);


--
-- Name: ix_provinces_code; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_provinces_code ON public.provinces USING btree (code);


--
-- Name: ix_provinces_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_provinces_uuid ON public.provinces USING btree (uuid);


--
-- Name: ix_sms_codes_phone; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_sms_codes_phone ON public.sms_codes USING btree (phone);


--
-- Name: ix_sms_codes_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_sms_codes_uuid ON public.sms_codes USING btree (uuid);


--
-- Name: ix_user_tokens_access_token_jti; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_user_tokens_access_token_jti ON public.user_tokens USING btree (access_token_jti);


--
-- Name: ix_user_tokens_refresh_token_jti; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_user_tokens_refresh_token_jti ON public.user_tokens USING btree (refresh_token_jti);


--
-- Name: ix_user_tokens_user_id; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX ix_user_tokens_user_id ON public.user_tokens USING btree (user_id);


--
-- Name: ix_user_tokens_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_user_tokens_uuid ON public.user_tokens USING btree (uuid);


--
-- Name: ix_users_phone; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_phone ON public.users USING btree (phone);


--
-- Name: ix_users_uuid; Type: INDEX; Schema: public; Owner: postgres
--

CREATE UNIQUE INDEX ix_users_uuid ON public.users USING btree (uuid);


--
-- Name: ai_conversations ai_conversations_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.ai_conversations
    ADD CONSTRAINT ai_conversations_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: car_comparisons car_comparisons_chat_message_uuid_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_comparisons
    ADD CONSTRAINT car_comparisons_chat_message_uuid_fkey FOREIGN KEY (chat_message_uuid) REFERENCES public.chat_messages(uuid);


--
-- Name: car_comparisons car_comparisons_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_comparisons
    ADD CONSTRAINT car_comparisons_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.ai_conversations(uuid);


--
-- Name: car_recommendations car_recommendations_chat_message_uuid_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_recommendations
    ADD CONSTRAINT car_recommendations_chat_message_uuid_fkey FOREIGN KEY (chat_message_uuid) REFERENCES public.chat_messages(uuid);


--
-- Name: car_recommendations car_recommendations_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.car_recommendations
    ADD CONSTRAINT car_recommendations_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.ai_conversations(uuid);


--
-- Name: chat_messages chat_messages_conversation_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_conversation_id_fkey FOREIGN KEY (conversation_id) REFERENCES public.ai_conversations(uuid);


--
-- Name: chat_messages chat_messages_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.chat_messages
    ADD CONSTRAINT chat_messages_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- Name: user_tokens user_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.user_tokens
    ADD CONSTRAINT user_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id);


--
-- PostgreSQL database dump complete
--

